# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
.venv/
.env/
ENV/
env.bak/
venv.bak/

# PyCharm
.idea/

# VS Code
.vscode/

# Jupyter Notebook
.ipynb_checkpoints

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Logs
logs
*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Machine Learning Models and Data
*.pkl
*.pickle
*.joblib
*.h5
*.hdf5
*.pb
*.onnx
*.tflite
*.pt
*.pth
*.bin
*.safetensors

# Large data files
*.csv
*.json
*.parquet
*.feather
*.xlsx
*.xls

# Vector indices and embeddings
models/indices/*
data/embeddings/*
data/processed/*

# Keep directory structure but ignore contents
!models/indices/.gitkeep
!data/embeddings/.gitkeep
!data/processed/.gitkeep
!data/raw/.gitkeep

# API Keys and Secrets
*.key
*.pem
secrets.json
config.json

# Test files
test_*.py
*_test.py
tests/

# Documentation build
docs/_build/

# Backup files
*.bak
*.backup
*.old

# Database files
*.db
*.sqlite
*.sqlite3

# Cache files
.cache/
*.cache

# Profiling data
.prof

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# FastAPI specific
.pytest_cache/

# Legal documents (for privacy)
data/raw/*.pdf
data/raw/*.docx
data/raw/*.doc
data/raw/*.txt

# Performance logs
performance_*.json
benchmark_*.json
