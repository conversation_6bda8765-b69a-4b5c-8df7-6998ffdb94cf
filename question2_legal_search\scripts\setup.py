#!/usr/bin/env python3
"""
Setup script for the Indian Legal Document Search System.
This script initializes the development environment and downloads required models.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description):
    """Run a shell command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def check_node_version():
    """Check if Node.js is installed."""
    try:
        result = subprocess.run("node --version", shell=True, capture_output=True, text=True)
        version = result.stdout.strip()
        print(f"✅ Node.js {version} detected")
        return True
    except:
        print("❌ Node.js not found. Please install Node.js 16 or higher")
        return False

def create_directories():
    """Create necessary directories."""
    directories = [
        "data/raw",
        "data/processed", 
        "data/embeddings",
        "models/embeddings",
        "models/indices",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        # Create .gitkeep files
        gitkeep_file = Path(directory) / ".gitkeep"
        gitkeep_file.touch()
    
    print("✅ Created necessary directories")

def setup_python_environment():
    """Set up Python virtual environment and install dependencies."""
    print("\n🐍 Setting up Python environment...")
    
    # Create virtual environment if it doesn't exist
    if not Path("venv").exists():
        if not run_command("python -m venv venv", "Creating virtual environment"):
            return False
    
    # Activate virtual environment and install dependencies
    if os.name == 'nt':  # Windows
        activate_cmd = "venv\\Scripts\\activate"
        pip_cmd = "venv\\Scripts\\pip"
    else:  # Unix/Linux/macOS
        activate_cmd = "source venv/bin/activate"
        pip_cmd = "venv/bin/pip"
    
    # Install dependencies
    if not run_command(f"{pip_cmd} install --upgrade pip", "Upgrading pip"):
        return False
    
    if not run_command(f"{pip_cmd} install -r backend/requirements.txt", "Installing Python dependencies"):
        return False
    
    return True

def download_spacy_model():
    """Download spaCy model for legal entity recognition."""
    if os.name == 'nt':  # Windows
        python_cmd = "venv\\Scripts\\python"
    else:  # Unix/Linux/macOS
        python_cmd = "venv/bin/python"
    
    return run_command(f"{python_cmd} -m spacy download en_core_web_sm", "Downloading spaCy model")

def setup_frontend():
    """Set up frontend dependencies."""
    print("\n⚛️ Setting up Frontend...")
    
    # Change to frontend directory
    os.chdir("frontend")
    
    # Install dependencies
    success = run_command("npm install", "Installing frontend dependencies")
    
    # Return to root directory
    os.chdir("..")
    
    return success

def create_env_file():
    """Create .env file from template."""
    if not Path(".env").exists():
        if Path(".env.example").exists():
            shutil.copy(".env.example", ".env")
            print("✅ Created .env file from template")
        else:
            print("⚠️ .env.example not found, please create .env manually")
    else:
        print("✅ .env file already exists")

def main():
    """Main setup function."""
    print("🚀 Setting up Indian Legal Document Search System")
    print("=" * 50)
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    if not check_node_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Create environment file
    create_env_file()
    
    # Set up Python environment
    if not setup_python_environment():
        print("❌ Failed to set up Python environment")
        sys.exit(1)
    
    # Download spaCy model
    if not download_spacy_model():
        print("⚠️ Failed to download spaCy model, you may need to install it manually")
    
    # Set up frontend
    if not setup_frontend():
        print("❌ Failed to set up frontend")
        sys.exit(1)
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env file with your configuration")
    print("2. Add legal documents to data/raw/ directory")
    print("3. Run 'python scripts/start_dev.py' to start development servers")

if __name__ == "__main__":
    main()
