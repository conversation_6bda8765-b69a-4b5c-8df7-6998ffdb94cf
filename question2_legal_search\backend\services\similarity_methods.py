"""
Implementation of all 4 similarity methods for legal document search:
1. Cosine Similarity
2. Euclidean Distance
3. Maximal Marginal Relevance (MMR)
4. Hybrid Similarity (Cosine + Legal Entity Matching)
"""

import numpy as np
from typing import List, Dict, Any, Tuple, Optional, Set
from dataclasses import dataclass
import logging
from abc import ABC, abstractmethod
import time

from core.config import settings
from services.text_chunker import TextChunk
from services.legal_ner import LegalEntity, EntityType

logger = logging.getLogger(__name__)

@dataclass
class SearchResult:
    """Search result with metadata."""
    chunk_id: str
    content: str
    similarity_score: float
    method_used: str
    metadata: Dict[str, Any]
    processing_time: float

class SimilarityMethod(ABC):
    """Abstract base class for similarity methods."""
    
    @abstractmethod
    def search(self, query_embedding: np.ndarray, 
              chunk_embeddings: Dict[str, np.ndarray],
              chunks: Dict[str, TextChunk],
              top_k: int = 10,
              **kwargs) -> List[SearchResult]:
        """
        Search for similar chunks.
        
        Args:
            query_embedding: Query embedding vector
            chunk_embeddings: Dictionary of chunk embeddings
            chunks: Dictionary of chunk objects
            top_k: Number of top results to return
            **kwargs: Additional method-specific parameters
            
        Returns:
            List of SearchResult objects
        """
        pass

class CosineSimilarity(SimilarityMethod):
    """Cosine similarity implementation."""
    
    def search(self, query_embedding: np.ndarray, 
              chunk_embeddings: Dict[str, np.ndarray],
              chunks: Dict[str, TextChunk],
              top_k: int = 10,
              **kwargs) -> List[SearchResult]:
        """Search using cosine similarity."""
        start_time = time.time()
        
        similarities = []
        
        # Normalize query embedding
        query_norm = np.linalg.norm(query_embedding)
        if query_norm == 0:
            logger.warning("Query embedding has zero norm")
            return []
        
        normalized_query = query_embedding / query_norm
        
        for chunk_id, chunk_embedding in chunk_embeddings.items():
            # Normalize chunk embedding
            chunk_norm = np.linalg.norm(chunk_embedding)
            if chunk_norm == 0:
                continue
            
            normalized_chunk = chunk_embedding / chunk_norm
            
            # Compute cosine similarity
            similarity = float(np.dot(normalized_query, normalized_chunk))
            similarities.append((chunk_id, similarity))
        
        # Sort by similarity (descending)
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        # Create results
        results = []
        processing_time = time.time() - start_time
        
        for chunk_id, similarity in similarities[:top_k]:
            if chunk_id in chunks:
                result = SearchResult(
                    chunk_id=chunk_id,
                    content=chunks[chunk_id].content,
                    similarity_score=similarity,
                    method_used="cosine",
                    metadata={
                        "chunk_metadata": chunks[chunk_id].metadata or {},
                        "similarity_details": {
                            "raw_score": similarity,
                            "normalized": True
                        }
                    },
                    processing_time=processing_time / len(similarities[:top_k])
                )
                results.append(result)
        
        return results

class EuclideanDistance(SimilarityMethod):
    """Euclidean distance similarity implementation."""
    
    def search(self, query_embedding: np.ndarray, 
              chunk_embeddings: Dict[str, np.ndarray],
              chunks: Dict[str, TextChunk],
              top_k: int = 10,
              **kwargs) -> List[SearchResult]:
        """Search using Euclidean distance (converted to similarity)."""
        start_time = time.time()
        
        distances = []
        
        for chunk_id, chunk_embedding in chunk_embeddings.items():
            # Compute Euclidean distance
            distance = float(np.linalg.norm(query_embedding - chunk_embedding))
            distances.append((chunk_id, distance))
        
        # Sort by distance (ascending)
        distances.sort(key=lambda x: x[1])
        
        # Convert distances to similarities (0-1 range)
        max_distance = max(dist for _, dist in distances) if distances else 1.0
        similarities = []
        
        for chunk_id, distance in distances:
            # Convert to similarity: closer = higher similarity
            similarity = 1.0 - (distance / max_distance) if max_distance > 0 else 1.0
            similarities.append((chunk_id, similarity))
        
        # Create results
        results = []
        processing_time = time.time() - start_time
        
        for chunk_id, similarity in similarities[:top_k]:
            if chunk_id in chunks:
                result = SearchResult(
                    chunk_id=chunk_id,
                    content=chunks[chunk_id].content,
                    similarity_score=similarity,
                    method_used="euclidean",
                    metadata={
                        "chunk_metadata": chunks[chunk_id].metadata or {},
                        "similarity_details": {
                            "raw_distance": distances[similarities.index((chunk_id, similarity))][1],
                            "max_distance": max_distance,
                            "normalized_similarity": similarity
                        }
                    },
                    processing_time=processing_time / len(similarities[:top_k])
                )
                results.append(result)
        
        return results

class MaximalMarginalRelevance(SimilarityMethod):
    """Maximal Marginal Relevance (MMR) implementation."""
    
    def search(self, query_embedding: np.ndarray, 
              chunk_embeddings: Dict[str, np.ndarray],
              chunks: Dict[str, TextChunk],
              top_k: int = 10,
              lambda_param: float = None,
              **kwargs) -> List[SearchResult]:
        """Search using MMR for diversity."""
        start_time = time.time()
        
        if lambda_param is None:
            lambda_param = settings.MMR_LAMBDA
        
        # First, get initial candidates using cosine similarity
        cosine_method = CosineSimilarity()
        initial_candidates = cosine_method.search(
            query_embedding, chunk_embeddings, chunks, 
            top_k=min(settings.MMR_K, len(chunk_embeddings))
        )
        
        if not initial_candidates:
            return []
        
        # MMR selection
        selected_results = []
        candidate_ids = [result.chunk_id for result in initial_candidates]
        candidate_similarities = {result.chunk_id: result.similarity_score for result in initial_candidates}
        
        while len(selected_results) < top_k and candidate_ids:
            best_score = -float('inf')
            best_candidate = None
            
            for candidate_id in candidate_ids:
                # Relevance score (similarity to query)
                relevance = candidate_similarities[candidate_id]
                
                # Diversity score (minimum similarity to already selected)
                if selected_results:
                    max_similarity_to_selected = 0.0
                    candidate_embedding = chunk_embeddings[candidate_id]
                    
                    for selected_result in selected_results:
                        selected_embedding = chunk_embeddings[selected_result.chunk_id]
                        
                        # Compute cosine similarity between candidate and selected
                        candidate_norm = np.linalg.norm(candidate_embedding)
                        selected_norm = np.linalg.norm(selected_embedding)
                        
                        if candidate_norm > 0 and selected_norm > 0:
                            similarity = np.dot(candidate_embedding, selected_embedding) / (candidate_norm * selected_norm)
                            max_similarity_to_selected = max(max_similarity_to_selected, similarity)
                    
                    diversity = 1.0 - max_similarity_to_selected
                else:
                    diversity = 1.0
                
                # MMR score
                mmr_score = lambda_param * relevance + (1 - lambda_param) * diversity
                
                if mmr_score > best_score:
                    best_score = mmr_score
                    best_candidate = candidate_id
            
            if best_candidate:
                # Create result for selected candidate
                original_result = next(r for r in initial_candidates if r.chunk_id == best_candidate)
                
                result = SearchResult(
                    chunk_id=best_candidate,
                    content=original_result.content,
                    similarity_score=best_score,  # MMR score
                    method_used="mmr",
                    metadata={
                        "chunk_metadata": original_result.metadata.get("chunk_metadata", {}),
                        "similarity_details": {
                            "mmr_score": best_score,
                            "relevance_score": candidate_similarities[best_candidate],
                            "lambda_param": lambda_param,
                            "position_in_mmr": len(selected_results) + 1
                        }
                    },
                    processing_time=(time.time() - start_time) / top_k
                )
                
                selected_results.append(result)
                candidate_ids.remove(best_candidate)
            else:
                break
        
        return selected_results

class HybridSimilarity(SimilarityMethod):
    """Hybrid similarity combining cosine similarity with legal entity matching."""
    
    def search(self, query_embedding: np.ndarray, 
              chunk_embeddings: Dict[str, np.ndarray],
              chunks: Dict[str, TextChunk],
              top_k: int = 10,
              query_entities: List[LegalEntity] = None,
              cosine_weight: float = None,
              entity_weight: float = None,
              **kwargs) -> List[SearchResult]:
        """Search using hybrid similarity."""
        start_time = time.time()
        
        if cosine_weight is None:
            cosine_weight = settings.HYBRID_COSINE_WEIGHT
        if entity_weight is None:
            entity_weight = settings.HYBRID_ENTITY_WEIGHT
        
        # Normalize weights
        total_weight = cosine_weight + entity_weight
        cosine_weight = cosine_weight / total_weight
        entity_weight = entity_weight / total_weight
        
        # Get cosine similarities
        cosine_method = CosineSimilarity()
        cosine_results = cosine_method.search(
            query_embedding, chunk_embeddings, chunks, 
            top_k=len(chunk_embeddings)  # Get all for reranking
        )
        
        # Create cosine similarity lookup
        cosine_scores = {result.chunk_id: result.similarity_score for result in cosine_results}
        
        # Compute entity similarity scores
        entity_scores = self._compute_entity_similarities(query_entities, chunks)
        
        # Combine scores
        hybrid_scores = []
        
        for chunk_id in chunk_embeddings.keys():
            cosine_score = cosine_scores.get(chunk_id, 0.0)
            entity_score = entity_scores.get(chunk_id, 0.0)
            
            hybrid_score = cosine_weight * cosine_score + entity_weight * entity_score
            hybrid_scores.append((chunk_id, hybrid_score, cosine_score, entity_score))
        
        # Sort by hybrid score
        hybrid_scores.sort(key=lambda x: x[1], reverse=True)
        
        # Create results
        results = []
        processing_time = time.time() - start_time
        
        for chunk_id, hybrid_score, cosine_score, entity_score in hybrid_scores[:top_k]:
            if chunk_id in chunks:
                result = SearchResult(
                    chunk_id=chunk_id,
                    content=chunks[chunk_id].content,
                    similarity_score=hybrid_score,
                    method_used="hybrid",
                    metadata={
                        "chunk_metadata": chunks[chunk_id].metadata or {},
                        "similarity_details": {
                            "hybrid_score": hybrid_score,
                            "cosine_score": cosine_score,
                            "entity_score": entity_score,
                            "cosine_weight": cosine_weight,
                            "entity_weight": entity_weight
                        }
                    },
                    processing_time=processing_time / len(hybrid_scores[:top_k])
                )
                results.append(result)
        
        return results
    
    def _compute_entity_similarities(self, query_entities: List[LegalEntity], 
                                   chunks: Dict[str, TextChunk]) -> Dict[str, float]:
        """Compute entity-based similarity scores."""
        if not query_entities:
            return {chunk_id: 0.0 for chunk_id in chunks.keys()}
        
        # Extract query entity types and normalized forms
        query_entity_types = set(entity.entity_type for entity in query_entities)
        query_entity_texts = set(entity.normalized_form or entity.text for entity in query_entities)
        
        entity_scores = {}
        
        for chunk_id, chunk in chunks.items():
            score = 0.0
            
            # Get chunk entities from metadata
            chunk_entities = chunk.metadata.get('entities', []) if chunk.metadata else []
            
            if chunk_entities:
                chunk_entity_types = set()
                chunk_entity_texts = set()
                
                for entity_data in chunk_entities:
                    entity_type = EntityType(entity_data.get('entity_type'))
                    entity_text = entity_data.get('normalized_form') or entity_data.get('text', '')
                    
                    chunk_entity_types.add(entity_type)
                    chunk_entity_texts.add(entity_text)
                
                # Type overlap score
                type_overlap = len(query_entity_types.intersection(chunk_entity_types))
                type_score = type_overlap / len(query_entity_types) if query_entity_types else 0.0
                
                # Text overlap score
                text_overlap = len(query_entity_texts.intersection(chunk_entity_texts))
                text_score = text_overlap / len(query_entity_texts) if query_entity_texts else 0.0
                
                # Combined entity score
                score = 0.6 * type_score + 0.4 * text_score
            
            entity_scores[chunk_id] = score
        
        return entity_scores
