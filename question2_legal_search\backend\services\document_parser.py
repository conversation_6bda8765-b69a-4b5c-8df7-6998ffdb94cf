"""
Document parser service for handling PDF, Word, and text files.
Specialized for legal document processing with metadata extraction.
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import logging

# Document processing libraries
import PyPDF2
import pdfplumber
from docx import Document
import docx2txt

# Configuration
from core.config import settings

logger = logging.getLogger(__name__)

@dataclass
class DocumentMetadata:
    """Document metadata structure."""
    filename: str
    file_type: str
    file_size: int
    page_count: Optional[int] = None
    title: Optional[str] = None
    author: Optional[str] = None
    creation_date: Optional[str] = None
    modification_date: Optional[str] = None
    legal_category: Optional[str] = None
    act_references: List[str] = None
    section_references: List[str] = None

@dataclass
class ParsedDocument:
    """Parsed document structure."""
    content: str
    metadata: DocumentMetadata
    sections: List[Dict[str, Any]] = None
    tables: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.sections is None:
            self.sections = []
        if self.tables is None:
            self.tables = []

class DocumentParser:
    """Main document parser class for legal documents."""
    
    def __init__(self):
        self.supported_extensions = settings.ALLOWED_EXTENSIONS
        self.max_file_size = settings.MAX_FILE_SIZE
        
    def parse_document(self, file_path: Path) -> ParsedDocument:
        """
        Parse a document and extract content with metadata.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            ParsedDocument with content and metadata
            
        Raises:
            ValueError: If file type not supported or file too large
            FileNotFoundError: If file doesn't exist
        """
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Check file size
        file_size = file_path.stat().st_size
        if file_size > self.max_file_size:
            raise ValueError(f"File too large: {file_size} bytes (max: {self.max_file_size})")
        
        # Check file extension
        file_extension = file_path.suffix.lower()
        if file_extension not in self.supported_extensions:
            raise ValueError(f"Unsupported file type: {file_extension}")
        
        logger.info(f"Parsing document: {file_path}")
        
        # Parse based on file type
        if file_extension == '.pdf':
            return self._parse_pdf(file_path)
        elif file_extension in ['.docx', '.doc']:
            return self._parse_word(file_path)
        elif file_extension == '.txt':
            return self._parse_text(file_path)
        else:
            raise ValueError(f"Unsupported file type: {file_extension}")
    
    def _parse_pdf(self, file_path: Path) -> ParsedDocument:
        """Parse PDF document using multiple methods for robustness."""
        content = ""
        metadata = self._create_base_metadata(file_path, "pdf")
        sections = []
        tables = []
        
        try:
            # Try pdfplumber first (better for tables and layout)
            with pdfplumber.open(file_path) as pdf:
                metadata.page_count = len(pdf.pages)
                
                for page_num, page in enumerate(pdf.pages, 1):
                    # Extract text
                    page_text = page.extract_text()
                    if page_text:
                        content += f"\n--- Page {page_num} ---\n"
                        content += page_text
                        
                        # Extract sections based on legal patterns
                        page_sections = self._extract_sections(page_text, page_num)
                        sections.extend(page_sections)
                    
                    # Extract tables
                    page_tables = page.extract_tables()
                    if page_tables:
                        for table_idx, table in enumerate(page_tables):
                            tables.append({
                                'page': page_num,
                                'table_index': table_idx,
                                'data': table
                            })
                
                # Extract PDF metadata
                if pdf.metadata:
                    metadata.title = pdf.metadata.get('Title')
                    metadata.author = pdf.metadata.get('Author')
                    metadata.creation_date = str(pdf.metadata.get('CreationDate'))
                    
        except Exception as e:
            logger.warning(f"pdfplumber failed for {file_path}, trying PyPDF2: {e}")
            
            # Fallback to PyPDF2
            try:
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    metadata.page_count = len(pdf_reader.pages)
                    
                    for page_num, page in enumerate(pdf_reader.pages, 1):
                        page_text = page.extract_text()
                        if page_text:
                            content += f"\n--- Page {page_num} ---\n"
                            content += page_text
                            
                            # Extract sections
                            page_sections = self._extract_sections(page_text, page_num)
                            sections.extend(page_sections)
                    
                    # Extract metadata
                    if pdf_reader.metadata:
                        metadata.title = pdf_reader.metadata.get('/Title')
                        metadata.author = pdf_reader.metadata.get('/Author')
                        metadata.creation_date = str(pdf_reader.metadata.get('/CreationDate'))
                        
            except Exception as e2:
                logger.error(f"Both PDF parsers failed for {file_path}: {e2}")
                raise ValueError(f"Failed to parse PDF: {e2}")
        
        # Post-process content
        content = self._clean_text(content)
        
        # Extract legal references
        metadata.act_references = self._extract_act_references(content)
        metadata.section_references = self._extract_section_references(content)
        metadata.legal_category = self._classify_legal_category(content)
        
        return ParsedDocument(
            content=content,
            metadata=metadata,
            sections=sections,
            tables=tables
        )
    
    def _parse_word(self, file_path: Path) -> ParsedDocument:
        """Parse Word document (.docx or .doc)."""
        content = ""
        metadata = self._create_base_metadata(file_path, "word")
        sections = []
        tables = []
        
        try:
            if file_path.suffix.lower() == '.docx':
                # Use python-docx for .docx files
                doc = Document(file_path)
                
                # Extract text from paragraphs
                for para in doc.paragraphs:
                    if para.text.strip():
                        content += para.text + "\n"
                
                # Extract tables
                for table_idx, table in enumerate(doc.tables):
                    table_data = []
                    for row in table.rows:
                        row_data = [cell.text.strip() for cell in row.cells]
                        table_data.append(row_data)
                    
                    tables.append({
                        'table_index': table_idx,
                        'data': table_data
                    })
                
                # Extract document properties
                if doc.core_properties:
                    metadata.title = doc.core_properties.title
                    metadata.author = doc.core_properties.author
                    metadata.creation_date = str(doc.core_properties.created)
                    metadata.modification_date = str(doc.core_properties.modified)
                    
            else:
                # Use docx2txt for .doc files
                content = docx2txt.process(file_path)
                
        except Exception as e:
            logger.error(f"Failed to parse Word document {file_path}: {e}")
            raise ValueError(f"Failed to parse Word document: {e}")
        
        # Post-process content
        content = self._clean_text(content)
        
        # Extract sections
        sections = self._extract_sections(content)
        
        # Extract legal references
        metadata.act_references = self._extract_act_references(content)
        metadata.section_references = self._extract_section_references(content)
        metadata.legal_category = self._classify_legal_category(content)
        
        return ParsedDocument(
            content=content,
            metadata=metadata,
            sections=sections,
            tables=tables
        )
    
    def _parse_text(self, file_path: Path) -> ParsedDocument:
        """Parse plain text file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
        except UnicodeDecodeError:
            # Try different encodings
            for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        content = file.read()
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise ValueError(f"Could not decode text file: {file_path}")
        
        metadata = self._create_base_metadata(file_path, "text")
        
        # Post-process content
        content = self._clean_text(content)
        
        # Extract sections
        sections = self._extract_sections(content)
        
        # Extract legal references
        metadata.act_references = self._extract_act_references(content)
        metadata.section_references = self._extract_section_references(content)
        metadata.legal_category = self._classify_legal_category(content)
        
        return ParsedDocument(
            content=content,
            metadata=metadata,
            sections=sections
        )
    
    def _create_base_metadata(self, file_path: Path, file_type: str) -> DocumentMetadata:
        """Create base metadata for a document."""
        file_stat = file_path.stat()
        
        return DocumentMetadata(
            filename=file_path.name,
            file_type=file_type,
            file_size=file_stat.st_size,
            creation_date=str(file_stat.st_ctime),
            modification_date=str(file_stat.st_mtime),
            act_references=[],
            section_references=[]
        )
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove page headers/footers patterns
        text = re.sub(r'--- Page \d+ ---', '', text)
        
        # Normalize line breaks
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        # Remove special characters but keep legal formatting
        text = re.sub(r'[^\w\s\.\,\;\:\!\?\(\)\[\]\-\'\"]', ' ', text)
        
        return text.strip()
    
    def _extract_sections(self, text: str, page_num: Optional[int] = None) -> List[Dict[str, Any]]:
        """Extract legal sections from text."""
        sections = []
        
        # Pattern for legal sections (e.g., "Section 80C", "Chapter 4", etc.)
        section_patterns = [
            r'(?:Section|Sec\.?)\s+(\d+[A-Z]*(?:\([a-z]\))?)',
            r'(?:Chapter|Ch\.?)\s+(\d+[A-Z]*)',
            r'(?:Article|Art\.?)\s+(\d+[A-Z]*)',
            r'(?:Rule|R\.?)\s+(\d+[A-Z]*)',
        ]
        
        for pattern in section_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                section_info = {
                    'type': match.group(0).split()[0].lower(),
                    'number': match.group(1),
                    'start_position': match.start(),
                    'text_snippet': text[match.start():match.start()+200]
                }
                
                if page_num:
                    section_info['page'] = page_num
                    
                sections.append(section_info)
        
        return sections
    
    def _extract_act_references(self, text: str) -> List[str]:
        """Extract references to legal acts."""
        act_patterns = [
            r'Income\s+Tax\s+Act(?:\s+\d{4})?',
            r'GST\s+Act(?:\s+\d{4})?',
            r'Goods\s+and\s+Services\s+Tax\s+Act(?:\s+\d{4})?',
            r'Property\s+Law(?:\s+Act)?(?:\s+\d{4})?',
            r'Registration\s+Act(?:\s+\d{4})?',
            r'Transfer\s+of\s+Property\s+Act(?:\s+\d{4})?',
        ]
        
        references = []
        for pattern in act_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            references.extend(matches)
        
        return list(set(references))  # Remove duplicates
    
    def _extract_section_references(self, text: str) -> List[str]:
        """Extract section references from text."""
        section_pattern = r'(?:Section|Sec\.?)\s+(\d+[A-Z]*(?:\([a-z]\))?)'
        matches = re.findall(section_pattern, text, re.IGNORECASE)
        return list(set(matches))
    
    def _classify_legal_category(self, text: str) -> Optional[str]:
        """Classify the legal category of the document."""
        text_lower = text.lower()
        
        # Define category keywords
        categories = {
            "Income Tax Act": ["income tax", "tax deduction", "tds", "assessment", "return"],
            "GST Act": ["gst", "goods and services tax", "input credit", "tax invoice"],
            "Court Judgments": ["judgment", "court", "petitioner", "respondent", "appeal"],
            "Property Law": ["property", "registration", "transfer", "ownership", "deed"]
        }
        
        # Score each category
        category_scores = {}
        for category, keywords in categories.items():
            score = sum(text_lower.count(keyword) for keyword in keywords)
            if score > 0:
                category_scores[category] = score
        
        # Return category with highest score
        if category_scores:
            return max(category_scores, key=category_scores.get)
        
        return None
