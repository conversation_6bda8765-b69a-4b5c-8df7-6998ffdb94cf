{"name": "legal-search-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "Frontend for Indian Legal Document Search System", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "lucide-react": "^0.294.0", "recharts": "^2.8.0", "react-hot-toast": "^2.4.1", "react-dropzone": "^14.2.3", "framer-motion": "^10.16.5", "three": "^0.158.0", "@react-three/fiber": "^8.15.11", "@react-three/drei": "^9.88.13", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/three": "^0.158.3", "@vitejs/plugin-react": "^4.1.1", "vite": "^5.0.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "vitest": "^0.34.6", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "jsdom": "^23.0.1"}, "engines": {"node": ">=16.0.0"}}