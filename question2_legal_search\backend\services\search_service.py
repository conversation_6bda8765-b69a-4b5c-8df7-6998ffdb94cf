"""
Main search service that orchestrates all similarity methods and provides
a unified interface for legal document search.
"""

import asyncio
import time
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

from core.config import settings
from services.document_processor import DocumentProcessingPipeline, ProcessedDocument
from services.embedding_service import EmbeddingService
from services.vector_store import VectorStore
from services.legal_ner import LegalNER, LegalEntity
from services.similarity_methods import (
    CosineSimilarity, EuclideanDistance, MaximalMarginalRelevance, 
    HybridSimilarity, SearchResult
)

logger = logging.getLogger(__name__)

@dataclass
class SearchQuery:
    """Search query with metadata."""
    text: str
    top_k: int = 5
    methods: List[str] = None
    filters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.methods is None:
            self.methods = settings.SIMILARITY_METHODS
        if self.filters is None:
            self.filters = {}

@dataclass
class SearchResponse:
    """Search response with results from all methods."""
    query: str
    results_by_method: Dict[str, List[SearchResult]]
    query_entities: List[LegalEntity]
    processing_time: float
    total_chunks_searched: int
    metadata: Dict[str, Any]

class LegalSearchService:
    """Main search service for legal documents."""
    
    def __init__(self):
        self.document_processor = DocumentProcessingPipeline()
        self.embedding_service = EmbeddingService()
        self.vector_store = VectorStore()
        self.ner = LegalNER()
        
        # Initialize similarity methods
        self.similarity_methods = {
            "cosine": CosineSimilarity(),
            "euclidean": EuclideanDistance(),
            "mmr": MaximalMarginalRelevance(),
            "hybrid": HybridSimilarity()
        }
        
        # Cache for processed documents and embeddings
        self.processed_documents: Dict[str, ProcessedDocument] = {}
        self.chunk_embeddings: Dict[str, Dict[str, Any]] = {}
        self.chunks_by_id: Dict[str, Any] = {}
        
        # Initialize indices
        self._initialize_indices()
    
    def _initialize_indices(self):
        """Initialize FAISS indices for different similarity methods."""
        for method in settings.SIMILARITY_METHODS:
            if method in ["cosine", "hybrid"]:
                self.vector_store.create_index("cosine")
            elif method == "euclidean":
                self.vector_store.create_index("euclidean")
    
    async def index_documents(self, document_paths: List[str]) -> Dict[str, Any]:
        """
        Index a list of documents for search.
        
        Args:
            document_paths: List of paths to documents to index
            
        Returns:
            Indexing statistics and results
        """
        start_time = time.time()
        
        logger.info(f"Starting indexing of {len(document_paths)} documents")
        
        # Process documents
        processed_docs = []
        for doc_path in document_paths:
            try:
                processed_doc = self.document_processor.process_document(doc_path)
                processed_docs.append(processed_doc)
                self.processed_documents[processed_doc.document_id] = processed_doc
            except Exception as e:
                logger.error(f"Failed to process document {doc_path}: {e}")
                continue
        
        if not processed_docs:
            return {"error": "No documents were successfully processed"}
        
        # Extract all chunks
        all_chunks = []
        for doc in processed_docs:
            all_chunks.extend(doc.chunks)
            for chunk in doc.chunks:
                self.chunks_by_id[chunk.chunk_id] = chunk
        
        logger.info(f"Extracted {len(all_chunks)} chunks from {len(processed_docs)} documents")
        
        # Generate embeddings
        chunk_texts = [chunk.content for chunk in all_chunks]
        chunk_ids = [chunk.chunk_id for chunk in all_chunks]
        
        embeddings = self.embedding_service.generate_chunk_embeddings(all_chunks)
        
        # Store embeddings
        self.chunk_embeddings = embeddings
        
        # Add to vector indices
        embedding_matrix = np.array([embeddings[chunk_id] for chunk_id in chunk_ids])
        
        # Add to cosine index
        self.vector_store.add_vectors("cosine", embedding_matrix, chunk_ids)
        
        # Add to euclidean index if it exists
        if "euclidean" in self.vector_store.list_indices():
            self.vector_store.add_vectors("euclidean", embedding_matrix, chunk_ids)
        
        # Save indices
        for index_name in self.vector_store.list_indices():
            self.vector_store.save_index(index_name)
        
        processing_time = time.time() - start_time
        
        stats = {
            "documents_processed": len(processed_docs),
            "total_chunks": len(all_chunks),
            "embeddings_generated": len(embeddings),
            "processing_time": processing_time,
            "indices_updated": self.vector_store.list_indices()
        }
        
        logger.info(f"Indexing completed in {processing_time:.2f} seconds")
        return stats
    
    async def search(self, query: SearchQuery) -> SearchResponse:
        """
        Perform search using all specified similarity methods.
        
        Args:
            query: Search query object
            
        Returns:
            Search response with results from all methods
        """
        start_time = time.time()
        
        logger.info(f"Searching for: '{query.text}' using methods: {query.methods}")
        
        # Extract entities from query
        query_entities = self.ner.extract_entities(query.text)
        
        # Generate query embedding
        query_embedding = self.embedding_service.generate_embeddings([query.text])[0]
        
        # Apply filters to get relevant chunks
        filtered_chunks = self._apply_filters(query.filters)
        filtered_embeddings = {
            chunk_id: embedding for chunk_id, embedding in self.chunk_embeddings.items()
            if chunk_id in filtered_chunks
        }
        
        # Run similarity methods in parallel
        results_by_method = {}
        
        if len(query.methods) == 1:
            # Single method - run directly
            method_name = query.methods[0]
            if method_name in self.similarity_methods:
                results = await self._run_similarity_method(
                    method_name, query_embedding, filtered_embeddings, 
                    filtered_chunks, query.top_k, query_entities
                )
                results_by_method[method_name] = results
        else:
            # Multiple methods - run in parallel
            with ThreadPoolExecutor(max_workers=settings.MAX_WORKERS) as executor:
                future_to_method = {}
                
                for method_name in query.methods:
                    if method_name in self.similarity_methods:
                        future = executor.submit(
                            self._run_similarity_method_sync,
                            method_name, query_embedding, filtered_embeddings,
                            filtered_chunks, query.top_k, query_entities
                        )
                        future_to_method[future] = method_name
                
                for future in as_completed(future_to_method):
                    method_name = future_to_method[future]
                    try:
                        results = future.result()
                        results_by_method[method_name] = results
                    except Exception as e:
                        logger.error(f"Error in {method_name} method: {e}")
                        results_by_method[method_name] = []
        
        processing_time = time.time() - start_time
        
        response = SearchResponse(
            query=query.text,
            results_by_method=results_by_method,
            query_entities=query_entities,
            processing_time=processing_time,
            total_chunks_searched=len(filtered_chunks),
            metadata={
                "methods_used": list(results_by_method.keys()),
                "filters_applied": query.filters,
                "query_embedding_shape": query_embedding.shape
            }
        )
        
        logger.info(f"Search completed in {processing_time:.2f} seconds")
        return response
    
    async def _run_similarity_method(self, method_name: str, query_embedding, 
                                   embeddings: Dict, chunks: Dict, top_k: int,
                                   query_entities: List[LegalEntity]) -> List[SearchResult]:
        """Run a similarity method asynchronously."""
        return self._run_similarity_method_sync(
            method_name, query_embedding, embeddings, chunks, top_k, query_entities
        )
    
    def _run_similarity_method_sync(self, method_name: str, query_embedding, 
                                  embeddings: Dict, chunks: Dict, top_k: int,
                                  query_entities: List[LegalEntity]) -> List[SearchResult]:
        """Run a similarity method synchronously."""
        method = self.similarity_methods[method_name]
        
        kwargs = {}
        if method_name == "hybrid":
            kwargs["query_entities"] = query_entities
        
        return method.search(
            query_embedding=query_embedding,
            chunk_embeddings=embeddings,
            chunks=chunks,
            top_k=top_k,
            **kwargs
        )
    
    def _apply_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """Apply filters to chunks."""
        if not filters:
            return self.chunks_by_id
        
        filtered_chunks = {}
        
        for chunk_id, chunk in self.chunks_by_id.items():
            include_chunk = True
            
            # Document type filter
            if "document_type" in filters:
                doc_id = chunk.document_id
                if doc_id in self.processed_documents:
                    doc_type = self.processed_documents[doc_id].metadata.get("file_type")
                    if doc_type not in filters["document_type"]:
                        include_chunk = False
            
            # Legal category filter
            if "legal_category" in filters and include_chunk:
                doc_id = chunk.document_id
                if doc_id in self.processed_documents:
                    category = self.processed_documents[doc_id].metadata.get("legal_category")
                    if category not in filters["legal_category"]:
                        include_chunk = False
            
            # Entity type filter
            if "entity_types" in filters and include_chunk:
                chunk_entity_types = chunk.metadata.get("entity_types", []) if chunk.metadata else []
                if not any(entity_type in filters["entity_types"] for entity_type in chunk_entity_types):
                    include_chunk = False
            
            # Minimum chunk size filter
            if "min_tokens" in filters and include_chunk:
                if chunk.token_count < filters["min_tokens"]:
                    include_chunk = False
            
            if include_chunk:
                filtered_chunks[chunk_id] = chunk
        
        return filtered_chunks
    
    def get_search_statistics(self) -> Dict[str, Any]:
        """Get statistics about the search system."""
        return {
            "indexed_documents": len(self.processed_documents),
            "total_chunks": len(self.chunks_by_id),
            "embeddings_cached": len(self.chunk_embeddings),
            "available_methods": list(self.similarity_methods.keys()),
            "vector_store_stats": self.vector_store.get_statistics(),
            "embedding_service_info": self.embedding_service.get_model_info()
        }
    
    def clear_index(self):
        """Clear all indexed data."""
        self.processed_documents.clear()
        self.chunk_embeddings.clear()
        self.chunks_by_id.clear()
        
        # Clear vector store indices
        for index_name in self.vector_store.list_indices():
            self.vector_store.delete_index(index_name)
        
        # Clear embedding cache
        self.embedding_service.clear_cache()
        
        logger.info("Search index cleared")

# Global search service instance
search_service = LegalSearchService()
