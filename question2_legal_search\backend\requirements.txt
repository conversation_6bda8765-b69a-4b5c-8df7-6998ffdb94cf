# Core Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# CORS and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Data Models and Validation
pydantic==2.5.0
pydantic-settings==2.1.0

# Machine Learning and NLP
torch==2.1.1
transformers==4.36.0
sentence-transformers==2.2.2
scikit-learn==1.3.2
numpy==1.24.4
scipy==1.11.4

# Vector Database and Search
faiss-cpu==1.7.4
# faiss-gpu==1.7.4  # Uncomment for GPU support

# Document Processing
PyPDF2==3.0.1
python-docx==1.1.0
pypdf==3.17.1
pdfplumber==0.10.0

# Natural Language Processing
spacy==3.7.2
nltk==3.8.1
textstat==0.7.3

# Legal Entity Recognition (will download spacy model separately)
# python -m spacy download en_core_web_sm

# Data Processing and Analysis
pandas==2.1.4
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# Async and Concurrency
asyncio-throttle==1.0.2
aiofiles==23.2.1

# Environment and Configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# Logging and Monitoring
loguru==0.7.2

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Database (if needed for metadata storage)
sqlalchemy==2.0.23
alembic==1.13.1

# Utilities
tqdm==4.66.1
click==8.1.7
pathlib2==2.3.7

# Performance and Optimization
numba==0.58.1
joblib==1.3.2

# Text Processing
regex==2023.10.3
unidecode==1.3.7

# Legal Document Specific
# Custom legal text processing utilities will be implemented

# Development Dependencies
black==23.11.0
flake8==6.1.0
isort==5.12.0
mypy==1.7.1

# Optional: OpenAI for advanced embeddings
# openai==1.3.7

# Optional: Hugging Face Hub
huggingface-hub==0.19.4

# Memory and Performance Monitoring
psutil==5.9.6
memory-profiler==0.61.0
