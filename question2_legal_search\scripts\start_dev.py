#!/usr/bin/env python3
"""
Development server startup script for the Indian Legal Document Search System.
This script starts both the backend and frontend development servers.
"""

import os
import sys
import subprocess
import threading
import time
import signal
from pathlib import Path

class DevServer:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True
    
    def start_backend(self):
        """Start the FastAPI backend server."""
        print("🐍 Starting backend server...")
        
        # Change to backend directory
        backend_dir = Path("backend")
        
        # Determine Python executable path
        if os.name == 'nt':  # Windows
            python_cmd = str(Path("venv/Scripts/python").resolve())
        else:  # Unix/Linux/macOS
            python_cmd = str(Path("venv/bin/python").resolve())
        
        try:
            self.backend_process = subprocess.Popen(
                [python_cmd, "main.py"],
                cwd=backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Monitor backend output
            def monitor_backend():
                for line in iter(self.backend_process.stdout.readline, ''):
                    if self.running:
                        print(f"[Backend] {line.strip()}")
                    else:
                        break
            
            backend_thread = threading.Thread(target=monitor_backend, daemon=True)
            backend_thread.start()
            
            print("✅ Backend server started on http://localhost:8000")
            
        except Exception as e:
            print(f"❌ Failed to start backend server: {e}")
            return False
        
        return True
    
    def start_frontend(self):
        """Start the React frontend development server."""
        print("⚛️ Starting frontend server...")
        
        # Change to frontend directory
        frontend_dir = Path("frontend")
        
        try:
            self.frontend_process = subprocess.Popen(
                ["npm", "run", "dev"],
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Monitor frontend output
            def monitor_frontend():
                for line in iter(self.frontend_process.stdout.readline, ''):
                    if self.running:
                        print(f"[Frontend] {line.strip()}")
                    else:
                        break
            
            frontend_thread = threading.Thread(target=monitor_frontend, daemon=True)
            frontend_thread.start()
            
            print("✅ Frontend server started on http://localhost:5173")
            
        except Exception as e:
            print(f"❌ Failed to start frontend server: {e}")
            return False
        
        return True
    
    def stop_servers(self):
        """Stop both servers."""
        print("\n🛑 Stopping development servers...")
        self.running = False
        
        if self.backend_process:
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=5)
                print("✅ Backend server stopped")
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
                print("⚠️ Backend server force killed")
        
        if self.frontend_process:
            self.frontend_process.terminate()
            try:
                self.frontend_process.wait(timeout=5)
                print("✅ Frontend server stopped")
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
                print("⚠️ Frontend server force killed")
    
    def signal_handler(self, signum, frame):
        """Handle interrupt signals."""
        self.stop_servers()
        sys.exit(0)

def check_prerequisites():
    """Check if the environment is set up correctly."""
    # Check if virtual environment exists
    if not Path("venv").exists():
        print("❌ Virtual environment not found. Please run 'python scripts/setup.py' first")
        return False
    
    # Check if backend dependencies are installed
    if not Path("backend/requirements.txt").exists():
        print("❌ Backend requirements.txt not found")
        return False
    
    # Check if frontend dependencies are installed
    if not Path("frontend/node_modules").exists():
        print("❌ Frontend dependencies not installed. Please run 'npm install' in frontend directory")
        return False
    
    # Check if .env file exists
    if not Path(".env").exists():
        print("⚠️ .env file not found. Using default configuration")
    
    return True

def main():
    """Main function to start development servers."""
    print("🚀 Starting Indian Legal Document Search System Development Servers")
    print("=" * 70)
    
    # Check prerequisites
    if not check_prerequisites():
        sys.exit(1)
    
    # Create dev server instance
    dev_server = DevServer()
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, dev_server.signal_handler)
    signal.signal(signal.SIGTERM, dev_server.signal_handler)
    
    try:
        # Start backend server
        if not dev_server.start_backend():
            sys.exit(1)
        
        # Wait a moment for backend to start
        time.sleep(2)
        
        # Start frontend server
        if not dev_server.start_frontend():
            dev_server.stop_servers()
            sys.exit(1)
        
        print("\n🎉 Development servers are running!")
        print("📱 Frontend: http://localhost:5173")
        print("🔧 Backend API: http://localhost:8000")
        print("📚 API Docs: http://localhost:8000/docs")
        print("\nPress Ctrl+C to stop the servers")
        
        # Keep the main thread alive
        while dev_server.running:
            time.sleep(1)
            
            # Check if processes are still running
            if dev_server.backend_process and dev_server.backend_process.poll() is not None:
                print("❌ Backend server stopped unexpectedly")
                break
            
            if dev_server.frontend_process and dev_server.frontend_process.poll() is not None:
                print("❌ Frontend server stopped unexpectedly")
                break
    
    except KeyboardInterrupt:
        pass
    finally:
        dev_server.stop_servers()

if __name__ == "__main__":
    main()
