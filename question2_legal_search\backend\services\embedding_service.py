"""
Embedding service using Sentence-BERT for legal document embeddings.
Handles embedding generation, caching, and batch processing.
"""

import numpy as np
import pickle
from pathlib import Path
from typing import List, Dict, Any, Optional, Union, Tuple
import logging
from datetime import datetime
import hashlib

from sentence_transformers import SentenceTransformer
import torch

from core.config import settings
from services.text_chunker import TextChunk

logger = logging.getLogger(__name__)

class EmbeddingService:
    """Service for generating and managing document embeddings."""
    
    def __init__(self):
        self.model = None
        self.model_name = settings.EMBEDDING_MODEL
        self.embedding_dimension = settings.EMBEDDING_DIMENSION
        self.batch_size = settings.BATCH_SIZE
        self.max_length = settings.MAX_LENGTH
        
        # Setup directories
        self.embeddings_dir = Path(settings.DATA_DIR) / "embeddings"
        self.models_dir = Path(settings.MODELS_DIR) / "embeddings"
        self.embeddings_dir.mkdir(parents=True, exist_ok=True)
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # Cache for embeddings
        self.embedding_cache = {}
        self.cache_file = self.embeddings_dir / "embedding_cache.pkl"
        
        # Initialize model
        self._load_model()
        self._load_cache()
    
    def _load_model(self):
        """Load Sentence-BERT model."""
        try:
            logger.info(f"Loading Sentence-BERT model: {self.model_name}")
            
            # Check if model exists locally
            local_model_path = self.models_dir / self.model_name
            if local_model_path.exists():
                self.model = SentenceTransformer(str(local_model_path))
                logger.info(f"Loaded local model from {local_model_path}")
            else:
                # Download model
                self.model = SentenceTransformer(self.model_name)
                # Save model locally
                self.model.save(str(local_model_path))
                logger.info(f"Downloaded and saved model to {local_model_path}")
            
            # Set device
            device = "cuda" if torch.cuda.is_available() else "cpu"
            self.model = self.model.to(device)
            logger.info(f"Model loaded on device: {device}")
            
            # Verify embedding dimension
            test_embedding = self.model.encode(["test"], show_progress_bar=False)
            actual_dimension = test_embedding.shape[1]
            if actual_dimension != self.embedding_dimension:
                logger.warning(f"Model dimension {actual_dimension} differs from config {self.embedding_dimension}")
                self.embedding_dimension = actual_dimension
            
        except Exception as e:
            logger.error(f"Failed to load embedding model: {e}")
            raise
    
    def generate_embeddings(self, texts: List[str], 
                          show_progress: bool = True) -> np.ndarray:
        """
        Generate embeddings for a list of texts.
        
        Args:
            texts: List of text strings to embed
            show_progress: Whether to show progress bar
            
        Returns:
            NumPy array of embeddings with shape (len(texts), embedding_dimension)
        """
        if not texts:
            return np.array([]).reshape(0, self.embedding_dimension)
        
        logger.info(f"Generating embeddings for {len(texts)} texts")
        
        try:
            # Generate embeddings in batches
            embeddings = self.model.encode(
                texts,
                batch_size=self.batch_size,
                show_progress_bar=show_progress,
                convert_to_numpy=True,
                normalize_embeddings=True  # L2 normalization for cosine similarity
            )
            
            logger.info(f"Generated embeddings with shape: {embeddings.shape}")
            return embeddings
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            raise
    
    def generate_chunk_embeddings(self, chunks: List[TextChunk], 
                                use_cache: bool = True) -> Dict[str, np.ndarray]:
        """
        Generate embeddings for text chunks with caching.
        
        Args:
            chunks: List of TextChunk objects
            use_cache: Whether to use cached embeddings
            
        Returns:
            Dictionary mapping chunk_id to embedding array
        """
        chunk_embeddings = {}
        texts_to_embed = []
        chunk_ids_to_embed = []
        
        # Check cache for existing embeddings
        for chunk in chunks:
            chunk_hash = self._calculate_text_hash(chunk.content)
            
            if use_cache and chunk_hash in self.embedding_cache:
                chunk_embeddings[chunk.chunk_id] = self.embedding_cache[chunk_hash]
            else:
                texts_to_embed.append(chunk.content)
                chunk_ids_to_embed.append((chunk.chunk_id, chunk_hash))
        
        # Generate embeddings for new texts
        if texts_to_embed:
            logger.info(f"Generating embeddings for {len(texts_to_embed)} new chunks")
            new_embeddings = self.generate_embeddings(texts_to_embed)
            
            # Store new embeddings
            for i, (chunk_id, chunk_hash) in enumerate(chunk_ids_to_embed):
                embedding = new_embeddings[i]
                chunk_embeddings[chunk_id] = embedding
                
                # Update cache
                if use_cache:
                    self.embedding_cache[chunk_hash] = embedding
        
        # Save cache
        if use_cache and texts_to_embed:
            self._save_cache()
        
        return chunk_embeddings
    
    def save_embeddings(self, embeddings: Dict[str, np.ndarray], 
                       filename: str) -> Path:
        """
        Save embeddings to disk.
        
        Args:
            embeddings: Dictionary mapping IDs to embedding arrays
            filename: Name of the file to save
            
        Returns:
            Path to saved file
        """
        output_path = self.embeddings_dir / f"{filename}.pkl"
        
        try:
            with open(output_path, 'wb') as f:
                pickle.dump(embeddings, f)
            
            logger.info(f"Saved {len(embeddings)} embeddings to {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Failed to save embeddings: {e}")
            raise
    
    def load_embeddings(self, filename: str) -> Optional[Dict[str, np.ndarray]]:
        """
        Load embeddings from disk.
        
        Args:
            filename: Name of the file to load
            
        Returns:
            Dictionary mapping IDs to embedding arrays, or None if file doesn't exist
        """
        input_path = self.embeddings_dir / f"{filename}.pkl"
        
        if not input_path.exists():
            logger.warning(f"Embeddings file not found: {input_path}")
            return None
        
        try:
            with open(input_path, 'rb') as f:
                embeddings = pickle.load(f)
            
            logger.info(f"Loaded {len(embeddings)} embeddings from {input_path}")
            return embeddings
            
        except Exception as e:
            logger.error(f"Failed to load embeddings: {e}")
            return None
    
    def compute_similarity(self, embedding1: np.ndarray, 
                          embedding2: np.ndarray, 
                          method: str = "cosine") -> float:
        """
        Compute similarity between two embeddings.
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            method: Similarity method ("cosine" or "euclidean")
            
        Returns:
            Similarity score
        """
        if method == "cosine":
            # Cosine similarity (embeddings are already normalized)
            return float(np.dot(embedding1, embedding2))
        elif method == "euclidean":
            # Convert Euclidean distance to similarity (0-1 range)
            distance = np.linalg.norm(embedding1 - embedding2)
            return float(1.0 / (1.0 + distance))
        else:
            raise ValueError(f"Unsupported similarity method: {method}")
    
    def find_similar_chunks(self, query_embedding: np.ndarray,
                           chunk_embeddings: Dict[str, np.ndarray],
                           top_k: int = 10,
                           method: str = "cosine") -> List[Tuple[str, float]]:
        """
        Find most similar chunks to a query embedding.
        
        Args:
            query_embedding: Query embedding vector
            chunk_embeddings: Dictionary of chunk embeddings
            top_k: Number of top results to return
            method: Similarity method
            
        Returns:
            List of (chunk_id, similarity_score) tuples, sorted by similarity
        """
        similarities = []
        
        for chunk_id, chunk_embedding in chunk_embeddings.items():
            similarity = self.compute_similarity(query_embedding, chunk_embedding, method)
            similarities.append((chunk_id, similarity))
        
        # Sort by similarity (descending)
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return similarities[:top_k]
    
    def get_embedding_statistics(self, embeddings: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """Get statistics about embeddings."""
        if not embeddings:
            return {}
        
        embedding_matrix = np.array(list(embeddings.values()))
        
        return {
            "count": len(embeddings),
            "dimension": embedding_matrix.shape[1],
            "mean_norm": float(np.mean(np.linalg.norm(embedding_matrix, axis=1))),
            "std_norm": float(np.std(np.linalg.norm(embedding_matrix, axis=1))),
            "mean_values": embedding_matrix.mean(axis=0).tolist(),
            "std_values": embedding_matrix.std(axis=0).tolist()
        }
    
    def _calculate_text_hash(self, text: str) -> str:
        """Calculate hash for text content."""
        return hashlib.md5(text.encode()).hexdigest()
    
    def _load_cache(self):
        """Load embedding cache from disk."""
        if self.cache_file.exists():
            try:
                with open(self.cache_file, 'rb') as f:
                    self.embedding_cache = pickle.load(f)
                logger.info(f"Loaded {len(self.embedding_cache)} cached embeddings")
            except Exception as e:
                logger.warning(f"Failed to load embedding cache: {e}")
                self.embedding_cache = {}
        else:
            self.embedding_cache = {}
    
    def _save_cache(self):
        """Save embedding cache to disk."""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self.embedding_cache, f)
            logger.debug(f"Saved {len(self.embedding_cache)} embeddings to cache")
        except Exception as e:
            logger.warning(f"Failed to save embedding cache: {e}")
    
    def clear_cache(self):
        """Clear embedding cache."""
        self.embedding_cache = {}
        if self.cache_file.exists():
            self.cache_file.unlink()
        logger.info("Embedding cache cleared")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        return {
            "model_name": self.model_name,
            "embedding_dimension": self.embedding_dimension,
            "max_length": self.max_length,
            "batch_size": self.batch_size,
            "device": str(self.model.device) if self.model else "not loaded",
            "cache_size": len(self.embedding_cache)
        }
