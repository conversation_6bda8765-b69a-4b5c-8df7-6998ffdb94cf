"""
Configuration settings for the Indian Legal Document Search System.
"""

import os
from pathlib import Path
from typing import List, Dict, Any
from pydantic_settings import BaseSettings
from pydantic import Field

# Project root directory
PROJECT_ROOT = Path(__file__).parent.parent.parent

class Settings(BaseSettings):
    """Application settings."""
    
    # API Configuration
    API_TITLE: str = "Indian Legal Document Search System"
    API_DESCRIPTION: str = "A comprehensive search system comparing 4 similarity methods for legal document retrieval"
    API_VERSION: str = "1.0.0"
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    API_RELOAD: bool = True
    
    # CORS Configuration
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:5174",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:5174"
    ]
    
    # File Paths
    DATA_DIR: Path = PROJECT_ROOT / "data"
    RAW_DATA_DIR: Path = DATA_DIR / "raw"
    PROCESSED_DATA_DIR: Path = DATA_DIR / "processed"
    EMBEDDINGS_DIR: Path = DATA_DIR / "embeddings"
    MODELS_DIR: Path = PROJECT_ROOT / "models"
    EMBEDDING_MODELS_DIR: Path = MODELS_DIR / "embeddings"
    INDICES_DIR: Path = MODELS_DIR / "indices"
    
    # Document Processing Configuration
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    ALLOWED_EXTENSIONS: List[str] = [".pdf", ".docx", ".doc", ".txt"]
    
    # Text Chunking Configuration
    CHUNK_SIZE: int = 800  # tokens per chunk
    CHUNK_OVERLAP: int = 100  # token overlap between chunks
    MIN_CHUNK_SIZE: int = 50  # minimum chunk size
    
    # Embedding Configuration
    EMBEDDING_MODEL: str = "all-MiniLM-L6-v2"
    EMBEDDING_DIMENSION: int = 384
    BATCH_SIZE: int = 32
    MAX_LENGTH: int = 512
    
    # FAISS Configuration
    FAISS_INDEX_TYPE: str = "IndexFlatIP"  # Inner Product for cosine similarity
    FAISS_NPROBE: int = 10
    FAISS_NLIST: int = 100
    
    # Similarity Methods Configuration
    SIMILARITY_METHODS: List[str] = [
        "cosine",
        "euclidean", 
        "mmr",
        "hybrid"
    ]
    
    # MMR Configuration
    MMR_LAMBDA: float = 0.7  # Balance between relevance and diversity
    MMR_K: int = 20  # Number of candidates to consider
    
    # Hybrid Similarity Configuration
    HYBRID_COSINE_WEIGHT: float = 0.6
    HYBRID_ENTITY_WEIGHT: float = 0.4
    
    # Search Configuration
    DEFAULT_TOP_K: int = 5
    MAX_TOP_K: int = 50
    
    # Legal Entity Recognition Configuration
    SPACY_MODEL: str = "en_core_web_sm"
    LEGAL_ENTITIES: List[str] = [
        "INCOME_TAX_ACT",
        "GST_ACT", 
        "COURT_NAME",
        "CASE_CITATION",
        "SECTION_NUMBER",
        "PROPERTY_LAW"
    ]
    
    # Performance Evaluation Configuration
    EVALUATION_METRICS: List[str] = [
        "precision",
        "recall", 
        "diversity_score",
        "response_time"
    ]
    
    # Test Queries
    TEST_QUERIES: List[str] = [
        "Income tax deduction for education",
        "GST rate for textile products", 
        "Property registration process",
        "Court fee structure"
    ]
    
    # Legal Document Categories
    DOCUMENT_CATEGORIES: List[str] = [
        "Income Tax Act",
        "GST Act",
        "Court Judgments", 
        "Property Law"
    ]
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE: str = "legal_search.log"
    
    # Database Configuration (if needed)
    DATABASE_URL: str = "sqlite:///./legal_search.db"
    
    # Cache Configuration
    CACHE_TTL: int = 3600  # 1 hour
    ENABLE_CACHE: bool = True
    
    # Performance Configuration
    MAX_WORKERS: int = 4
    TIMEOUT_SECONDS: int = 30
    
    # Development Configuration
    DEBUG: bool = False
    TESTING: bool = False
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

# Global settings instance
settings = Settings()

# Ensure directories exist
def create_directories():
    """Create necessary directories if they don't exist."""
    directories = [
        settings.DATA_DIR,
        settings.RAW_DATA_DIR,
        settings.PROCESSED_DATA_DIR,
        settings.EMBEDDINGS_DIR,
        settings.MODELS_DIR,
        settings.EMBEDDING_MODELS_DIR,
        settings.INDICES_DIR
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

# Legal document patterns for entity recognition
LEGAL_PATTERNS = {
    "section_pattern": r"Section\s+(\d+[A-Z]*)",
    "act_pattern": r"(Income\s+Tax\s+Act|GST\s+Act|Property\s+Law)",
    "court_pattern": r"(Supreme\s+Court|High\s+Court|District\s+Court)",
    "case_citation_pattern": r"\d{4}\s+\w+\s+\d+"
}

# Performance thresholds
PERFORMANCE_THRESHOLDS = {
    "precision_threshold": 0.7,
    "recall_threshold": 0.6,
    "diversity_threshold": 0.5,
    "response_time_threshold": 2.0  # seconds
}
