"""
Legal Named Entity Recognition (NER) system for Indian legal documents.
Identifies acts, sections, courts, case citations, and other legal entities.
"""

import re
from typing import List, Dict, Any, Set, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import logging

import spacy
from spacy.matcher import Matcher
from spacy.tokens import Doc, Span

from core.config import settings

logger = logging.getLogger(__name__)

class EntityType(Enum):
    """Legal entity types."""
    ACT = "ACT"
    SECTION = "SECTION"
    COURT = "COURT"
    CASE_CITATION = "CASE_CITATION"
    JUDGE = "JUDGE"
    LEGAL_PROVISION = "LEGAL_PROVISION"
    DATE = "DATE"
    AMOUNT = "AMOUNT"
    PARTY = "PARTY"

@dataclass
class LegalEntity:
    """Legal entity with metadata."""
    text: str
    entity_type: EntityType
    start_position: int
    end_position: int
    confidence: float
    normalized_form: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class LegalNER:
    """Legal Named Entity Recognition system."""
    
    def __init__(self):
        self.nlp = None
        self.matcher = None
        self._load_model()
        self._setup_patterns()
    
    def _load_model(self):
        """Load spaCy model."""
        try:
            self.nlp = spacy.load(settings.SPACY_MODEL)
            logger.info(f"Loaded spaCy model: {settings.SPACY_MODEL}")
        except OSError:
            logger.warning(f"Could not load {settings.SPACY_MODEL}, using blank model")
            self.nlp = spacy.blank("en")
        
        # Add custom pipeline components
        if "legal_matcher" not in self.nlp.pipe_names:
            self.nlp.add_pipe("legal_matcher", last=True)
    
    def _setup_patterns(self):
        """Setup pattern matching for legal entities."""
        self.matcher = Matcher(self.nlp.vocab)
        
        # Indian Acts patterns
        act_patterns = [
            [{"LOWER": "income"}, {"LOWER": "tax"}, {"LOWER": "act"}, {"IS_DIGIT": True, "OP": "?"}],
            [{"LOWER": "gst"}, {"LOWER": "act"}, {"IS_DIGIT": True, "OP": "?"}],
            [{"LOWER": "goods"}, {"LOWER": "and"}, {"LOWER": "services"}, {"LOWER": "tax"}, {"LOWER": "act"}, {"IS_DIGIT": True, "OP": "?"}],
            [{"LOWER": "companies"}, {"LOWER": "act"}, {"IS_DIGIT": True, "OP": "?"}],
            [{"LOWER": "transfer"}, {"LOWER": "of"}, {"LOWER": "property"}, {"LOWER": "act"}, {"IS_DIGIT": True, "OP": "?"}],
            [{"LOWER": "registration"}, {"LOWER": "act"}, {"IS_DIGIT": True, "OP": "?"}],
            [{"LOWER": "indian"}, {"LOWER": "penal"}, {"LOWER": "code"}],
            [{"LOWER": "code"}, {"LOWER": "of"}, {"LOWER": "civil"}, {"LOWER": "procedure"}],
            [{"LOWER": "code"}, {"LOWER": "of"}, {"LOWER": "criminal"}, {"LOWER": "procedure"}],
            [{"LOWER": "constitution"}, {"LOWER": "of"}, {"LOWER": "india"}],
        ]
        
        # Section patterns
        section_patterns = [
            [{"LOWER": {"IN": ["section", "sec", "sec."]}}, {"IS_DIGIT": True}, {"IS_ALPHA": True, "OP": "?"}],
            [{"LOWER": {"IN": ["article", "art", "art."]}}, {"IS_DIGIT": True}, {"IS_ALPHA": True, "OP": "?"}],
            [{"LOWER": {"IN": ["rule", "r", "r."]}}, {"IS_DIGIT": True}, {"IS_ALPHA": True, "OP": "?"}],
            [{"LOWER": {"IN": ["chapter", "ch", "ch."]}}, {"IS_DIGIT": True, "OP": "?"}, {"IS_ALPHA": True, "OP": "?"}],
        ]
        
        # Court patterns
        court_patterns = [
            [{"LOWER": "supreme"}, {"LOWER": "court"}, {"LOWER": "of"}, {"LOWER": "india"}],
            [{"LOWER": {"IN": ["high", "hc"]}}, {"LOWER": "court"}],
            [{"LOWER": "district"}, {"LOWER": "court"}],
            [{"LOWER": "sessions"}, {"LOWER": "court"}],
            [{"LOWER": "magistrate"}, {"LOWER": "court"}],
            [{"LOWER": "tribunal"}],
            [{"LOWER": "appellate"}, {"LOWER": "tribunal"}],
            [{"LOWER": "income"}, {"LOWER": "tax"}, {"LOWER": "appellate"}, {"LOWER": "tribunal"}],
            [{"LOWER": "itat"}],
            [{"LOWER": "cestat"}],
        ]
        
        # Case citation patterns
        citation_patterns = [
            [{"IS_DIGIT": True}, {"IS_DIGIT": True}, {"IS_DIGIT": True}, {"IS_DIGIT": True}, 
             {"LOWER": {"IN": ["sc", "scc", "scr", "air", "all", "bom", "cal", "del", "mad", "ker"]}}, 
             {"IS_DIGIT": True, "OP": "?"}],
        ]
        
        # Add patterns to matcher
        self.matcher.add("ACT", act_patterns)
        self.matcher.add("SECTION", section_patterns)
        self.matcher.add("COURT", court_patterns)
        self.matcher.add("CITATION", citation_patterns)
    
    def extract_entities(self, text: str) -> List[LegalEntity]:
        """
        Extract legal entities from text.
        
        Args:
            text: Input text to process
            
        Returns:
            List of LegalEntity objects
        """
        if not text or not text.strip():
            return []
        
        doc = self.nlp(text)
        entities = []
        
        # Extract using pattern matcher
        pattern_entities = self._extract_pattern_entities(doc)
        entities.extend(pattern_entities)
        
        # Extract using regex patterns
        regex_entities = self._extract_regex_entities(text)
        entities.extend(regex_entities)
        
        # Extract using spaCy NER
        spacy_entities = self._extract_spacy_entities(doc)
        entities.extend(spacy_entities)
        
        # Remove duplicates and overlaps
        entities = self._remove_overlaps(entities)
        
        # Sort by position
        entities.sort(key=lambda x: x.start_position)
        
        return entities
    
    def _extract_pattern_entities(self, doc: Doc) -> List[LegalEntity]:
        """Extract entities using pattern matching."""
        entities = []
        matches = self.matcher(doc)
        
        for match_id, start, end in matches:
            span = doc[start:end]
            label = self.nlp.vocab.strings[match_id]
            
            entity_type = self._map_label_to_entity_type(label)
            if entity_type:
                entity = LegalEntity(
                    text=span.text,
                    entity_type=entity_type,
                    start_position=span.start_char,
                    end_position=span.end_char,
                    confidence=0.9,  # High confidence for pattern matches
                    normalized_form=self._normalize_entity(span.text, entity_type)
                )
                entities.append(entity)
        
        return entities
    
    def _extract_regex_entities(self, text: str) -> List[LegalEntity]:
        """Extract entities using regex patterns."""
        entities = []
        
        # Define regex patterns
        patterns = {
            EntityType.CASE_CITATION: [
                r'\b\d{4}\s+\d+\s+(?:SCC|SCR|AIR|ALL|BOM|CAL|DEL|MAD|KER)\s+\d+\b',
                r'\b(?:AIR|SCC|SCR)\s+\d{4}\s+(?:SC|SCC)\s+\d+\b',
                r'\b\d{4}\s+\(\d+\)\s+(?:SCC|SCR|AIR)\s+\d+\b',
            ],
            EntityType.AMOUNT: [
                r'Rs\.?\s*\d+(?:,\d+)*(?:\.\d+)?(?:\s*(?:crore|lakh|thousand))?',
                r'₹\s*\d+(?:,\d+)*(?:\.\d+)?(?:\s*(?:crore|lakh|thousand))?',
                r'\b\d+(?:,\d+)*(?:\.\d+)?\s*(?:crore|lakh|thousand)\b',
            ],
            EntityType.DATE: [
                r'\b\d{1,2}[/-]\d{1,2}[/-]\d{4}\b',
                r'\b\d{1,2}(?:st|nd|rd|th)?\s+(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}\b',
                r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4}\b',
            ],
            EntityType.JUDGE: [
                r'\b(?:Justice|J\.)\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b',
                r'\b(?:Hon\'ble|Honble)\s+(?:Justice|J\.)\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b',
                r'\b(?:Chief Justice|CJ)\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b',
            ]
        }
        
        for entity_type, pattern_list in patterns.items():
            for pattern in pattern_list:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    entity = LegalEntity(
                        text=match.group(),
                        entity_type=entity_type,
                        start_position=match.start(),
                        end_position=match.end(),
                        confidence=0.8,  # Medium confidence for regex
                        normalized_form=self._normalize_entity(match.group(), entity_type)
                    )
                    entities.append(entity)
        
        return entities
    
    def _extract_spacy_entities(self, doc: Doc) -> List[LegalEntity]:
        """Extract entities using spaCy's built-in NER."""
        entities = []
        
        for ent in doc.ents:
            # Map spaCy labels to our entity types
            entity_type = self._map_spacy_label(ent.label_)
            if entity_type:
                entity = LegalEntity(
                    text=ent.text,
                    entity_type=entity_type,
                    start_position=ent.start_char,
                    end_position=ent.end_char,
                    confidence=0.7,  # Lower confidence for general NER
                    normalized_form=self._normalize_entity(ent.text, entity_type),
                    metadata={"spacy_label": ent.label_}
                )
                entities.append(entity)
        
        return entities
    
    def _map_label_to_entity_type(self, label: str) -> Optional[EntityType]:
        """Map pattern matcher labels to entity types."""
        mapping = {
            "ACT": EntityType.ACT,
            "SECTION": EntityType.SECTION,
            "COURT": EntityType.COURT,
            "CITATION": EntityType.CASE_CITATION,
        }
        return mapping.get(label)
    
    def _map_spacy_label(self, label: str) -> Optional[EntityType]:
        """Map spaCy NER labels to our entity types."""
        mapping = {
            "PERSON": EntityType.PARTY,
            "ORG": EntityType.COURT,
            "DATE": EntityType.DATE,
            "MONEY": EntityType.AMOUNT,
            "LAW": EntityType.ACT,
        }
        return mapping.get(label)
    
    def _normalize_entity(self, text: str, entity_type: EntityType) -> str:
        """Normalize entity text."""
        text = text.strip()
        
        if entity_type == EntityType.ACT:
            # Normalize act names
            text = re.sub(r'\s+', ' ', text)
            text = text.title()
        elif entity_type == EntityType.SECTION:
            # Normalize section references
            text = re.sub(r'(?i)sec\.?', 'Section', text)
            text = re.sub(r'(?i)art\.?', 'Article', text)
        elif entity_type == EntityType.COURT:
            # Normalize court names
            text = text.title()
            text = re.sub(r'\bHc\b', 'High Court', text, flags=re.IGNORECASE)
        elif entity_type == EntityType.AMOUNT:
            # Normalize amounts
            text = re.sub(r'Rs\.?', '₹', text)
            text = re.sub(r'\s+', ' ', text)
        
        return text
    
    def _remove_overlaps(self, entities: List[LegalEntity]) -> List[LegalEntity]:
        """Remove overlapping entities, keeping the one with higher confidence."""
        if not entities:
            return entities
        
        # Sort by start position, then by confidence (descending)
        entities.sort(key=lambda x: (x.start_position, -x.confidence))
        
        filtered_entities = []
        for entity in entities:
            # Check for overlap with existing entities
            overlaps = False
            for existing in filtered_entities:
                if (entity.start_position < existing.end_position and 
                    entity.end_position > existing.start_position):
                    overlaps = True
                    break
            
            if not overlaps:
                filtered_entities.append(entity)
        
        return filtered_entities
    
    def get_entity_statistics(self, entities: List[LegalEntity]) -> Dict[str, Any]:
        """Get statistics about extracted entities."""
        stats = {
            "total_entities": len(entities),
            "entity_types": {},
            "confidence_distribution": {
                "high": 0,  # >= 0.8
                "medium": 0,  # 0.6 - 0.8
                "low": 0  # < 0.6
            }
        }
        
        for entity in entities:
            # Count by type
            entity_type = entity.entity_type.value
            stats["entity_types"][entity_type] = stats["entity_types"].get(entity_type, 0) + 1
            
            # Count by confidence
            if entity.confidence >= 0.8:
                stats["confidence_distribution"]["high"] += 1
            elif entity.confidence >= 0.6:
                stats["confidence_distribution"]["medium"] += 1
            else:
                stats["confidence_distribution"]["low"] += 1
        
        return stats

# Custom spaCy pipeline component
@spacy.Language.component("legal_matcher")
def legal_matcher_component(doc):
    """Custom spaCy pipeline component for legal entity matching."""
    # This component can be extended to add custom logic
    return doc
