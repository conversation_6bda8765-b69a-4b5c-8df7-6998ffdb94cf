"""
Document preprocessing pipeline that orchestrates the entire process
from raw documents to processed, chunked text ready for embedding.
"""

import json
import pickle
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import logging
import hashlib
from datetime import datetime

from services.document_parser import DocumentParser, ParsedDocument
from services.text_chunker import LegalTextChunker, TextChunk
from services.legal_ner import LegalNER, LegalEntity
from core.config import settings

logger = logging.getLogger(__name__)

@dataclass
class ProcessedDocument:
    """Complete processed document with all metadata."""
    document_id: str
    original_filename: str
    content: str
    chunks: List[TextChunk]
    entities: List[LegalEntity]
    metadata: Dict[str, Any]
    processing_timestamp: str
    content_hash: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "document_id": self.document_id,
            "original_filename": self.original_filename,
            "content": self.content,
            "chunks": [asdict(chunk) for chunk in self.chunks],
            "entities": [asdict(entity) for entity in self.entities],
            "metadata": self.metadata,
            "processing_timestamp": self.processing_timestamp,
            "content_hash": self.content_hash
        }

class DocumentProcessingPipeline:
    """End-to-end document processing pipeline."""
    
    def __init__(self):
        self.parser = DocumentParser()
        self.chunker = LegalTextChunker()
        self.ner = LegalNER()
        
        # Ensure output directories exist
        self.processed_dir = Path(settings.DATA_DIR) / "processed"
        self.processed_dir.mkdir(parents=True, exist_ok=True)
        
        # Cache for processed documents
        self.cache_file = self.processed_dir / "processing_cache.json"
        self.cache = self._load_cache()
    
    def process_document(self, file_path: Path, force_reprocess: bool = False) -> ProcessedDocument:
        """
        Process a single document through the complete pipeline.
        
        Args:
            file_path: Path to the document file
            force_reprocess: Force reprocessing even if cached
            
        Returns:
            ProcessedDocument with all processing results
        """
        document_id = self._generate_document_id(file_path)
        
        # Check cache first
        if not force_reprocess and document_id in self.cache:
            cached_doc = self._load_processed_document(document_id)
            if cached_doc:
                logger.info(f"Loaded cached document: {document_id}")
                return cached_doc
        
        logger.info(f"Processing document: {file_path}")
        
        try:
            # Step 1: Parse document
            parsed_doc = self.parser.parse_document(file_path)
            logger.info(f"Parsed document with {len(parsed_doc.content)} characters")
            
            # Step 2: Extract legal entities
            entities = self.ner.extract_entities(parsed_doc.content)
            logger.info(f"Extracted {len(entities)} legal entities")
            
            # Step 3: Chunk text
            chunks = self.chunker.chunk_document(parsed_doc.content, document_id)
            logger.info(f"Created {len(chunks)} text chunks")
            
            # Step 4: Enhance chunks with entity information
            enhanced_chunks = self._enhance_chunks_with_entities(chunks, entities)
            
            # Step 5: Create processed document
            content_hash = self._calculate_content_hash(parsed_doc.content)
            processed_doc = ProcessedDocument(
                document_id=document_id,
                original_filename=file_path.name,
                content=parsed_doc.content,
                chunks=enhanced_chunks,
                entities=entities,
                metadata=asdict(parsed_doc.metadata),
                processing_timestamp=datetime.now().isoformat(),
                content_hash=content_hash
            )
            
            # Step 6: Save processed document
            self._save_processed_document(processed_doc)
            
            # Step 7: Update cache
            self._update_cache(document_id, file_path, content_hash)
            
            logger.info(f"Successfully processed document: {document_id}")
            return processed_doc
            
        except Exception as e:
            logger.error(f"Failed to process document {file_path}: {e}")
            raise
    
    def process_directory(self, directory_path: Path, 
                         file_extensions: Optional[List[str]] = None) -> List[ProcessedDocument]:
        """
        Process all documents in a directory.
        
        Args:
            directory_path: Path to directory containing documents
            file_extensions: List of file extensions to process (default: all supported)
            
        Returns:
            List of ProcessedDocument objects
        """
        if file_extensions is None:
            file_extensions = settings.ALLOWED_EXTENSIONS
        
        processed_docs = []
        
        # Find all files with supported extensions
        files_to_process = []
        for ext in file_extensions:
            files_to_process.extend(directory_path.glob(f"*{ext}"))
        
        logger.info(f"Found {len(files_to_process)} files to process in {directory_path}")
        
        for file_path in files_to_process:
            try:
                processed_doc = self.process_document(file_path)
                processed_docs.append(processed_doc)
            except Exception as e:
                logger.error(f"Failed to process {file_path}: {e}")
                continue
        
        logger.info(f"Successfully processed {len(processed_docs)} documents")
        return processed_docs
    
    def get_processing_statistics(self, processed_docs: List[ProcessedDocument]) -> Dict[str, Any]:
        """Get statistics about processed documents."""
        if not processed_docs:
            return {}
        
        total_docs = len(processed_docs)
        total_chunks = sum(len(doc.chunks) for doc in processed_docs)
        total_entities = sum(len(doc.entities) for doc in processed_docs)
        
        # File type distribution
        file_types = {}
        for doc in processed_docs:
            file_type = doc.metadata.get('file_type', 'unknown')
            file_types[file_type] = file_types.get(file_type, 0) + 1
        
        # Legal category distribution
        legal_categories = {}
        for doc in processed_docs:
            category = doc.metadata.get('legal_category', 'unknown')
            legal_categories[category] = legal_categories.get(category, 0) + 1
        
        # Chunk size statistics
        chunk_sizes = [chunk.token_count for doc in processed_docs for chunk in doc.chunks]
        avg_chunk_size = sum(chunk_sizes) / len(chunk_sizes) if chunk_sizes else 0
        
        # Entity type distribution
        entity_types = {}
        for doc in processed_docs:
            for entity in doc.entities:
                entity_type = entity.entity_type.value
                entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
        
        return {
            "total_documents": total_docs,
            "total_chunks": total_chunks,
            "total_entities": total_entities,
            "average_chunks_per_document": total_chunks / total_docs,
            "average_entities_per_document": total_entities / total_docs,
            "average_chunk_size": avg_chunk_size,
            "file_type_distribution": file_types,
            "legal_category_distribution": legal_categories,
            "entity_type_distribution": entity_types
        }
    
    def _enhance_chunks_with_entities(self, chunks: List[TextChunk], 
                                    entities: List[LegalEntity]) -> List[TextChunk]:
        """Enhance chunks with entity information."""
        for chunk in chunks:
            # Find entities that overlap with this chunk
            chunk_entities = []
            for entity in entities:
                if (entity.start_position < chunk.end_position and 
                    entity.end_position > chunk.start_position):
                    chunk_entities.append(entity)
            
            # Update chunk metadata with entity information
            if chunk_entities:
                chunk.metadata = chunk.metadata or {}
                chunk.metadata['entities'] = [asdict(entity) for entity in chunk_entities]
                
                # Add entity types to chunk for easier filtering
                entity_types = list(set(entity.entity_type.value for entity in chunk_entities))
                chunk.metadata['entity_types'] = entity_types
        
        return chunks
    
    def _generate_document_id(self, file_path: Path) -> str:
        """Generate unique document ID."""
        # Use file path and modification time for uniqueness
        file_stat = file_path.stat()
        unique_string = f"{file_path}_{file_stat.st_mtime}_{file_stat.st_size}"
        return hashlib.md5(unique_string.encode()).hexdigest()
    
    def _calculate_content_hash(self, content: str) -> str:
        """Calculate hash of document content."""
        return hashlib.sha256(content.encode()).hexdigest()
    
    def _save_processed_document(self, processed_doc: ProcessedDocument):
        """Save processed document to disk."""
        output_file = self.processed_dir / f"{processed_doc.document_id}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_doc.to_dict(), f, indent=2, ensure_ascii=False)
        
        logger.debug(f"Saved processed document to {output_file}")
    
    def _load_processed_document(self, document_id: str) -> Optional[ProcessedDocument]:
        """Load processed document from disk."""
        input_file = self.processed_dir / f"{document_id}.json"
        
        if not input_file.exists():
            return None
        
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Reconstruct objects
            chunks = [TextChunk(**chunk_data) for chunk_data in data['chunks']]
            entities = [LegalEntity(**entity_data) for entity_data in data['entities']]
            
            return ProcessedDocument(
                document_id=data['document_id'],
                original_filename=data['original_filename'],
                content=data['content'],
                chunks=chunks,
                entities=entities,
                metadata=data['metadata'],
                processing_timestamp=data['processing_timestamp'],
                content_hash=data['content_hash']
            )
            
        except Exception as e:
            logger.error(f"Failed to load processed document {document_id}: {e}")
            return None
    
    def _load_cache(self) -> Dict[str, Any]:
        """Load processing cache."""
        if self.cache_file.exists():
            try:
                with open(self.cache_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load cache: {e}")
        
        return {}
    
    def _update_cache(self, document_id: str, file_path: Path, content_hash: str):
        """Update processing cache."""
        self.cache[document_id] = {
            "file_path": str(file_path),
            "content_hash": content_hash,
            "processed_timestamp": datetime.now().isoformat()
        }
        
        try:
            with open(self.cache_file, 'w') as f:
                json.dump(self.cache, f, indent=2)
        except Exception as e:
            logger.warning(f"Failed to update cache: {e}")
    
    def clear_cache(self):
        """Clear processing cache."""
        self.cache = {}
        if self.cache_file.exists():
            self.cache_file.unlink()
        logger.info("Processing cache cleared")
    
    def get_processed_documents(self) -> List[str]:
        """Get list of processed document IDs."""
        return [f.stem for f in self.processed_dir.glob("*.json") if f.name != "processing_cache.json"]
