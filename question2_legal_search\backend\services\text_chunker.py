"""
Legal-aware text chunking service that preserves legal section boundaries
and maintains context for better semantic understanding.
"""

import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import logging

from core.config import settings

logger = logging.getLogger(__name__)

@dataclass
class TextChunk:
    """Text chunk with metadata."""
    content: str
    chunk_id: str
    start_position: int
    end_position: int
    token_count: int
    legal_section: Optional[str] = None
    section_type: Optional[str] = None
    act_references: List[str] = None
    section_references: List[str] = None
    chunk_type: str = "content"  # content, header, footer, table
    
    def __post_init__(self):
        if self.act_references is None:
            self.act_references = []
        if self.section_references is None:
            self.section_references = []

class LegalTextChunker:
    """Legal-aware text chunking system."""
    
    def __init__(self):
        self.chunk_size = settings.CHUNK_SIZE
        self.chunk_overlap = settings.CHUNK_OVERLAP
        self.min_chunk_size = settings.MIN_CHUNK_SIZE
        
        # Legal section patterns
        self.section_patterns = [
            r'(?:Section|Sec\.?)\s+(\d+[A-Z]*(?:\([a-z]\))?)',
            r'(?:Chapter|Ch\.?)\s+(\d+[A-Z]*)',
            r'(?:Article|Art\.?)\s+(\d+[A-Z]*)',
            r'(?:Rule|R\.?)\s+(\d+[A-Z]*)',
            r'(?:Part|Pt\.?)\s+([IVX]+|\d+)',
            r'(?:Schedule|Sch\.?)\s+([IVX]+|\d+)',
        ]
        
        # Legal boundary patterns (stronger section breaks)
        self.boundary_patterns = [
            r'\n\s*(?:CHAPTER|Chapter)\s+[IVX\d]+',
            r'\n\s*(?:PART|Part)\s+[IVX\d]+',
            r'\n\s*(?:SECTION|Section)\s+\d+',
            r'\n\s*(?:SCHEDULE|Schedule)\s+[IVX\d]+',
        ]
    
    def chunk_document(self, content: str, document_id: str) -> List[TextChunk]:
        """
        Chunk document content while preserving legal structure.
        
        Args:
            content: Document content to chunk
            document_id: Unique identifier for the document
            
        Returns:
            List of TextChunk objects
        """
        if not content or len(content.strip()) < self.min_chunk_size:
            return []
        
        logger.info(f"Chunking document {document_id} with {len(content)} characters")
        
        # First, identify legal sections
        legal_sections = self._identify_legal_sections(content)
        
        # Create chunks respecting legal boundaries
        chunks = self._create_legal_aware_chunks(content, legal_sections, document_id)
        
        # Post-process chunks
        chunks = self._post_process_chunks(chunks)
        
        logger.info(f"Created {len(chunks)} chunks for document {document_id}")
        return chunks
    
    def _identify_legal_sections(self, content: str) -> List[Dict[str, Any]]:
        """Identify legal sections in the content."""
        sections = []
        
        # Find all section boundaries
        for pattern in self.boundary_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                section_info = {
                    'start': match.start(),
                    'end': match.end(),
                    'text': match.group().strip(),
                    'type': self._extract_section_type(match.group()),
                    'number': self._extract_section_number(match.group())
                }
                sections.append(section_info)
        
        # Sort sections by position
        sections.sort(key=lambda x: x['start'])
        
        # Add section end positions
        for i, section in enumerate(sections):
            if i < len(sections) - 1:
                section['content_end'] = sections[i + 1]['start']
            else:
                section['content_end'] = len(content)
        
        return sections
    
    def _create_legal_aware_chunks(self, content: str, sections: List[Dict[str, Any]], 
                                 document_id: str) -> List[TextChunk]:
        """Create chunks that respect legal section boundaries."""
        chunks = []
        chunk_counter = 0
        
        if not sections:
            # No legal sections found, use standard chunking
            return self._create_standard_chunks(content, document_id)
        
        # Process each legal section
        for i, section in enumerate(sections):
            section_start = section['start']
            section_end = section['content_end']
            section_content = content[section_start:section_end]
            
            # Skip very small sections
            if len(section_content.strip()) < self.min_chunk_size:
                continue
            
            # If section is small enough, create single chunk
            if self._estimate_token_count(section_content) <= self.chunk_size:
                chunk = self._create_chunk(
                    content=section_content,
                    chunk_id=f"{document_id}_chunk_{chunk_counter}",
                    start_position=section_start,
                    end_position=section_end,
                    legal_section=section['text'],
                    section_type=section['type']
                )
                chunks.append(chunk)
                chunk_counter += 1
            else:
                # Section is too large, split it while preserving subsections
                section_chunks = self._split_large_section(
                    section_content, section, document_id, chunk_counter, section_start
                )
                chunks.extend(section_chunks)
                chunk_counter += len(section_chunks)
        
        return chunks
    
    def _split_large_section(self, section_content: str, section_info: Dict[str, Any],
                           document_id: str, start_counter: int, 
                           global_start: int) -> List[TextChunk]:
        """Split a large legal section into smaller chunks."""
        chunks = []
        
        # Try to find subsection boundaries
        subsection_patterns = [
            r'\n\s*\(\d+\)',  # (1), (2), etc.
            r'\n\s*\([a-z]\)',  # (a), (b), etc.
            r'\n\s*[a-z]\.',  # a., b., etc.
            r'\n\s*\d+\.',  # 1., 2., etc.
        ]
        
        subsections = []
        for pattern in subsection_patterns:
            matches = re.finditer(pattern, section_content, re.MULTILINE)
            for match in matches:
                subsections.append({
                    'start': match.start(),
                    'text': match.group().strip()
                })
        
        if subsections:
            # Split by subsections
            subsections.sort(key=lambda x: x['start'])
            
            for i, subsection in enumerate(subsections):
                start_pos = subsection['start']
                end_pos = subsections[i + 1]['start'] if i < len(subsections) - 1 else len(section_content)
                
                subsection_content = section_content[start_pos:end_pos]
                
                if self._estimate_token_count(subsection_content) <= self.chunk_size:
                    chunk = self._create_chunk(
                        content=subsection_content,
                        chunk_id=f"{document_id}_chunk_{start_counter + i}",
                        start_position=global_start + start_pos,
                        end_position=global_start + end_pos,
                        legal_section=section_info['text'],
                        section_type=section_info['type']
                    )
                    chunks.append(chunk)
                else:
                    # Even subsection is too large, use sliding window
                    sub_chunks = self._create_sliding_window_chunks(
                        subsection_content, document_id, start_counter + i,
                        global_start + start_pos, section_info
                    )
                    chunks.extend(sub_chunks)
        else:
            # No subsections found, use sliding window
            chunks = self._create_sliding_window_chunks(
                section_content, document_id, start_counter,
                global_start, section_info
            )
        
        return chunks
    
    def _create_sliding_window_chunks(self, content: str, document_id: str,
                                    start_counter: int, global_start: int,
                                    section_info: Dict[str, Any]) -> List[TextChunk]:
        """Create overlapping chunks using sliding window approach."""
        chunks = []
        words = content.split()
        
        # Estimate words per chunk
        words_per_chunk = int(self.chunk_size * 0.75)  # Conservative estimate
        overlap_words = int(self.chunk_overlap * 0.75)
        
        start_idx = 0
        chunk_counter = start_counter
        
        while start_idx < len(words):
            end_idx = min(start_idx + words_per_chunk, len(words))
            chunk_words = words[start_idx:end_idx]
            chunk_content = ' '.join(chunk_words)
            
            # Calculate positions
            start_pos = global_start + len(' '.join(words[:start_idx]))
            end_pos = global_start + len(' '.join(words[:end_idx]))
            
            chunk = self._create_chunk(
                content=chunk_content,
                chunk_id=f"{document_id}_chunk_{chunk_counter}",
                start_position=start_pos,
                end_position=end_pos,
                legal_section=section_info['text'],
                section_type=section_info['type']
            )
            chunks.append(chunk)
            
            # Move window
            start_idx += words_per_chunk - overlap_words
            chunk_counter += 1
            
            # Break if we've reached the end
            if end_idx >= len(words):
                break
        
        return chunks
    
    def _create_standard_chunks(self, content: str, document_id: str) -> List[TextChunk]:
        """Create standard chunks when no legal structure is detected."""
        chunks = []
        words = content.split()
        
        words_per_chunk = int(self.chunk_size * 0.75)
        overlap_words = int(self.chunk_overlap * 0.75)
        
        start_idx = 0
        chunk_counter = 0
        
        while start_idx < len(words):
            end_idx = min(start_idx + words_per_chunk, len(words))
            chunk_words = words[start_idx:end_idx]
            chunk_content = ' '.join(chunk_words)
            
            start_pos = len(' '.join(words[:start_idx]))
            end_pos = len(' '.join(words[:end_idx]))
            
            chunk = self._create_chunk(
                content=chunk_content,
                chunk_id=f"{document_id}_chunk_{chunk_counter}",
                start_position=start_pos,
                end_position=end_pos
            )
            chunks.append(chunk)
            
            start_idx += words_per_chunk - overlap_words
            chunk_counter += 1
            
            if end_idx >= len(words):
                break
        
        return chunks
    
    def _create_chunk(self, content: str, chunk_id: str, start_position: int,
                     end_position: int, legal_section: Optional[str] = None,
                     section_type: Optional[str] = None) -> TextChunk:
        """Create a TextChunk object with metadata."""
        # Extract legal references
        act_references = self._extract_act_references(content)
        section_references = self._extract_section_references(content)
        
        return TextChunk(
            content=content.strip(),
            chunk_id=chunk_id,
            start_position=start_position,
            end_position=end_position,
            token_count=self._estimate_token_count(content),
            legal_section=legal_section,
            section_type=section_type,
            act_references=act_references,
            section_references=section_references
        )
    
    def _post_process_chunks(self, chunks: List[TextChunk]) -> List[TextChunk]:
        """Post-process chunks to ensure quality."""
        processed_chunks = []
        
        for chunk in chunks:
            # Skip very small chunks
            if len(chunk.content.strip()) < self.min_chunk_size:
                continue
            
            # Clean content
            chunk.content = self._clean_chunk_content(chunk.content)
            
            # Update token count
            chunk.token_count = self._estimate_token_count(chunk.content)
            
            processed_chunks.append(chunk)
        
        return processed_chunks
    
    def _clean_chunk_content(self, content: str) -> str:
        """Clean chunk content."""
        # Remove excessive whitespace
        content = re.sub(r'\s+', ' ', content)
        
        # Remove incomplete sentences at the beginning
        sentences = content.split('.')
        if len(sentences) > 1 and len(sentences[0]) < 20:
            content = '.'.join(sentences[1:])
        
        return content.strip()
    
    def _estimate_token_count(self, text: str) -> int:
        """Estimate token count (rough approximation)."""
        return len(text.split()) * 1.3  # Approximate tokens per word
    
    def _extract_section_type(self, text: str) -> str:
        """Extract section type from text."""
        text_lower = text.lower()
        if 'chapter' in text_lower:
            return 'chapter'
        elif 'section' in text_lower:
            return 'section'
        elif 'part' in text_lower:
            return 'part'
        elif 'schedule' in text_lower:
            return 'schedule'
        elif 'article' in text_lower:
            return 'article'
        elif 'rule' in text_lower:
            return 'rule'
        return 'unknown'
    
    def _extract_section_number(self, text: str) -> Optional[str]:
        """Extract section number from text."""
        # Pattern to match numbers, roman numerals, or alphanumeric
        pattern = r'(\d+[A-Z]*(?:\([a-z]\))?|[IVX]+|\d+)'
        match = re.search(pattern, text)
        return match.group(1) if match else None
    
    def _extract_act_references(self, text: str) -> List[str]:
        """Extract act references from chunk."""
        act_patterns = [
            r'Income\s+Tax\s+Act(?:\s+\d{4})?',
            r'GST\s+Act(?:\s+\d{4})?',
            r'Property\s+Law(?:\s+Act)?(?:\s+\d{4})?',
        ]
        
        references = []
        for pattern in act_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            references.extend(matches)
        
        return list(set(references))
    
    def _extract_section_references(self, text: str) -> List[str]:
        """Extract section references from chunk."""
        pattern = r'(?:Section|Sec\.?)\s+(\d+[A-Z]*(?:\([a-z]\))?)'
        matches = re.findall(pattern, text, re.IGNORECASE)
        return list(set(matches))
