@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply text-slate-900 bg-gradient-to-br from-slate-50 to-blue-50;
  }
  
  * {
    @apply border-slate-200;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700 focus:ring-primary-500 shadow-soft hover:shadow-medium transform hover:scale-105;
  }
  
  .btn-secondary {
    @apply btn bg-white text-slate-700 border border-slate-300 hover:bg-slate-50 focus:ring-slate-500 shadow-soft hover:shadow-medium transform hover:scale-105;
  }
  
  .btn-success {
    @apply btn bg-gradient-to-r from-success-500 to-success-600 text-white hover:from-success-600 hover:to-success-700 focus:ring-success-500 shadow-soft hover:shadow-medium transform hover:scale-105;
  }
  
  .btn-warning {
    @apply btn bg-gradient-to-r from-warning-500 to-warning-600 text-white hover:from-warning-600 hover:to-warning-700 focus:ring-warning-500 shadow-soft hover:shadow-medium transform hover:scale-105;
  }
  
  .btn-error {
    @apply btn bg-gradient-to-r from-error-500 to-error-600 text-white hover:from-error-600 hover:to-error-700 focus:ring-error-500 shadow-soft hover:shadow-medium transform hover:scale-105;
  }
  
  /* Card Components */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-slate-200 transition-all duration-300 ease-in-out hover:shadow-medium;
  }
  
  .card-hover {
    @apply card hover:scale-105 hover:shadow-large cursor-pointer;
  }
  
  .card-legal {
    @apply card bg-gradient-to-br from-legal-50 to-legal-100 border-legal-200;
  }
  
  /* Input Components */
  .input {
    @apply block w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 ease-in-out;
  }
  
  .input-large {
    @apply input px-4 py-3 text-lg;
  }
  
  /* Text Components */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent;
  }
  
  .text-legal-gradient {
    @apply bg-gradient-to-r from-legal-600 to-legal-800 bg-clip-text text-transparent;
  }
  
  /* Layout Components */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-12 sm:py-16 lg:py-20;
  }
  
  /* Animation Components */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
  }
  
  /* Loading Components */
  .loading-spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
  }
  
  .loading-dots {
    @apply inline-flex space-x-1;
  }
  
  .loading-dots > div {
    @apply w-2 h-2 bg-current rounded-full animate-pulse;
  }
  
  /* Scrollbar Styling */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(148 163 184) rgb(241 245 249);
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(241 245 249);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(148 163 184);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(100 116 139);
  }
}

@layer utilities {
  /* Custom animations */
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
  
  @keyframes pulseGlow {
    0%, 100% { 
      box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
      transform: scale(1);
    }
    50% { 
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
      transform: scale(1.02);
    }
  }
  
  /* Glassmorphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }
  
  /* Text selection */
  ::selection {
    background: rgba(59, 130, 246, 0.2);
  }
  
  /* Focus visible */
  .focus-visible {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
}
