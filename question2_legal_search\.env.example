# Environment Configuration for Indian Legal Document Search System

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true
DEBUG=false
TESTING=false

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000","http://localhost:5173","http://localhost:5174"]

# Embedding Model Configuration
EMBEDDING_MODEL=all-MiniLM-L6-v2
EMBEDDING_DIMENSION=384
BATCH_SIZE=32
MAX_LENGTH=512

# Document Processing Configuration
MAX_FILE_SIZE=52428800  # 50MB in bytes
CHUNK_SIZE=800
CHUNK_OVERLAP=100
MIN_CHUNK_SIZE=50

# FAISS Configuration
FAISS_INDEX_TYPE=IndexFlatIP
FAISS_NPROBE=10
FAISS_NLIST=100

# MMR Configuration
MMR_LAMBDA=0.7
MMR_K=20

# Hybrid Similarity Configuration
HYBRID_COSINE_WEIGHT=0.6
HYBRID_ENTITY_WEIGHT=0.4

# Search Configuration
DEFAULT_TOP_K=5
MAX_TOP_K=50

# Legal Entity Recognition
SPACY_MODEL=en_core_web_sm

# Performance Configuration
MAX_WORKERS=4
TIMEOUT_SECONDS=30

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=legal_search.log

# Database Configuration (if needed)
DATABASE_URL=sqlite:///./legal_search.db

# Cache Configuration
CACHE_TTL=3600
ENABLE_CACHE=true

# Optional: OpenAI API (for advanced embeddings)
# OPENAI_API_KEY=your_openai_api_key_here

# Optional: Hugging Face Token (for private models)
# HUGGINGFACE_TOKEN=your_huggingface_token_here

# Development Configuration
FRONTEND_URL=http://localhost:5173
BACKEND_URL=http://localhost:8000

# Performance Thresholds
PRECISION_THRESHOLD=0.7
RECALL_THRESHOLD=0.6
DIVERSITY_THRESHOLD=0.5
RESPONSE_TIME_THRESHOLD=2.0
