"""
FAISS vector database service for efficient similarity search.
Supports multiple index types for different similarity methods.
"""

import numpy as np
import pickle
import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
import logging
from datetime import datetime

import faiss

from core.config import settings
from services.text_chunker import TextChunk

logger = logging.getLogger(__name__)

class VectorStore:
    """FAISS-based vector store for legal document chunks."""
    
    def __init__(self):
        self.dimension = settings.EMBEDDING_DIMENSION
        self.indices_dir = Path(settings.MODELS_DIR) / "indices"
        self.indices_dir.mkdir(parents=True, exist_ok=True)
        
        # Multiple indices for different similarity methods
        self.indices = {}
        self.chunk_metadata = {}
        
        # Index configurations
        self.index_configs = {
            "cosine": {
                "index_type": "IndexFlatIP",  # Inner Product for cosine similarity
                "description": "Flat index for cosine similarity (inner product)"
            },
            "euclidean": {
                "index_type": "IndexFlatL2",  # L2 distance for Euclidean
                "description": "Flat index for Euclidean distance"
            },
            "ivf_cosine": {
                "index_type": "IndexIVFFlat",  # IVF for faster search
                "description": "IVF index for cosine similarity",
                "nlist": settings.FAISS_NLIST
            },
            "ivf_euclidean": {
                "index_type": "IndexIVFFlat",
                "description": "IVF index for Euclidean distance",
                "nlist": settings.FAISS_NLIST
            }
        }
    
    def create_index(self, index_name: str, force_recreate: bool = False) -> bool:
        """
        Create a FAISS index.
        
        Args:
            index_name: Name of the index to create
            force_recreate: Force recreation even if index exists
            
        Returns:
            True if index was created successfully
        """
        if index_name not in self.index_configs:
            raise ValueError(f"Unknown index type: {index_name}")
        
        index_path = self.indices_dir / f"{index_name}.index"
        metadata_path = self.indices_dir / f"{index_name}_metadata.json"
        
        # Check if index already exists
        if not force_recreate and index_path.exists():
            logger.info(f"Index {index_name} already exists, loading...")
            return self.load_index(index_name)
        
        config = self.index_configs[index_name]
        
        try:
            # Create appropriate index type
            if config["index_type"] == "IndexFlatIP":
                index = faiss.IndexFlatIP(self.dimension)
            elif config["index_type"] == "IndexFlatL2":
                index = faiss.IndexFlatL2(self.dimension)
            elif config["index_type"] == "IndexIVFFlat":
                # Create IVF index
                quantizer = faiss.IndexFlatL2(self.dimension) if "euclidean" in index_name else faiss.IndexFlatIP(self.dimension)
                index = faiss.IndexIVFFlat(quantizer, self.dimension, config["nlist"])
            else:
                raise ValueError(f"Unsupported index type: {config['index_type']}")
            
            self.indices[index_name] = index
            
            # Initialize metadata
            self.chunk_metadata[index_name] = {
                "chunk_ids": [],
                "creation_time": datetime.now().isoformat(),
                "config": config,
                "total_vectors": 0
            }
            
            logger.info(f"Created {index_name} index with {config['description']}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create index {index_name}: {e}")
            return False
    
    def add_vectors(self, index_name: str, vectors: np.ndarray, 
                   chunk_ids: List[str]) -> bool:
        """
        Add vectors to an index.
        
        Args:
            index_name: Name of the index
            vectors: Array of vectors to add
            chunk_ids: List of chunk IDs corresponding to vectors
            
        Returns:
            True if vectors were added successfully
        """
        if index_name not in self.indices:
            logger.error(f"Index {index_name} not found")
            return False
        
        if len(vectors) != len(chunk_ids):
            logger.error("Number of vectors must match number of chunk IDs")
            return False
        
        try:
            index = self.indices[index_name]
            
            # Ensure vectors are float32
            vectors = vectors.astype(np.float32)
            
            # For IVF indices, train if not already trained
            if hasattr(index, 'is_trained') and not index.is_trained:
                logger.info(f"Training IVF index {index_name}...")
                index.train(vectors)
            
            # Add vectors to index
            index.add(vectors)
            
            # Update metadata
            metadata = self.chunk_metadata[index_name]
            metadata["chunk_ids"].extend(chunk_ids)
            metadata["total_vectors"] = index.ntotal
            metadata["last_update"] = datetime.now().isoformat()
            
            logger.info(f"Added {len(vectors)} vectors to {index_name} index")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add vectors to {index_name}: {e}")
            return False
    
    def search(self, index_name: str, query_vector: np.ndarray, 
              top_k: int = 10) -> Tuple[List[float], List[str]]:
        """
        Search for similar vectors in an index.
        
        Args:
            index_name: Name of the index to search
            query_vector: Query vector
            top_k: Number of top results to return
            
        Returns:
            Tuple of (similarities, chunk_ids)
        """
        if index_name not in self.indices:
            logger.error(f"Index {index_name} not found")
            return [], []
        
        try:
            index = self.indices[index_name]
            
            # Ensure query vector is float32 and 2D
            query_vector = query_vector.astype(np.float32)
            if query_vector.ndim == 1:
                query_vector = query_vector.reshape(1, -1)
            
            # Set search parameters for IVF indices
            if hasattr(index, 'nprobe'):
                index.nprobe = settings.FAISS_NPROBE
            
            # Perform search
            similarities, indices = index.search(query_vector, top_k)
            
            # Get chunk IDs for the results
            chunk_ids = []
            metadata = self.chunk_metadata[index_name]
            
            for idx in indices[0]:
                if 0 <= idx < len(metadata["chunk_ids"]):
                    chunk_ids.append(metadata["chunk_ids"][idx])
                else:
                    chunk_ids.append(None)  # Invalid index
            
            # Convert similarities to list
            similarities = similarities[0].tolist()
            
            # Filter out invalid results
            valid_results = [(sim, cid) for sim, cid in zip(similarities, chunk_ids) if cid is not None]
            
            if valid_results:
                similarities, chunk_ids = zip(*valid_results)
                return list(similarities), list(chunk_ids)
            else:
                return [], []
            
        except Exception as e:
            logger.error(f"Failed to search in {index_name}: {e}")
            return [], []
    
    def save_index(self, index_name: str) -> bool:
        """
        Save an index to disk.
        
        Args:
            index_name: Name of the index to save
            
        Returns:
            True if index was saved successfully
        """
        if index_name not in self.indices:
            logger.error(f"Index {index_name} not found")
            return False
        
        try:
            index_path = self.indices_dir / f"{index_name}.index"
            metadata_path = self.indices_dir / f"{index_name}_metadata.json"
            
            # Save FAISS index
            faiss.write_index(self.indices[index_name], str(index_path))
            
            # Save metadata
            with open(metadata_path, 'w') as f:
                json.dump(self.chunk_metadata[index_name], f, indent=2)
            
            logger.info(f"Saved index {index_name} to {index_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save index {index_name}: {e}")
            return False
    
    def load_index(self, index_name: str) -> bool:
        """
        Load an index from disk.
        
        Args:
            index_name: Name of the index to load
            
        Returns:
            True if index was loaded successfully
        """
        index_path = self.indices_dir / f"{index_name}.index"
        metadata_path = self.indices_dir / f"{index_name}_metadata.json"
        
        if not index_path.exists() or not metadata_path.exists():
            logger.warning(f"Index files not found for {index_name}")
            return False
        
        try:
            # Load FAISS index
            index = faiss.read_index(str(index_path))
            self.indices[index_name] = index
            
            # Load metadata
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            self.chunk_metadata[index_name] = metadata
            
            logger.info(f"Loaded index {index_name} with {index.ntotal} vectors")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load index {index_name}: {e}")
            return False
    
    def get_index_info(self, index_name: str) -> Optional[Dict[str, Any]]:
        """Get information about an index."""
        if index_name not in self.indices:
            return None
        
        index = self.indices[index_name]
        metadata = self.chunk_metadata.get(index_name, {})
        
        return {
            "name": index_name,
            "type": metadata.get("config", {}).get("index_type", "unknown"),
            "description": metadata.get("config", {}).get("description", ""),
            "dimension": index.d,
            "total_vectors": index.ntotal,
            "is_trained": getattr(index, 'is_trained', True),
            "creation_time": metadata.get("creation_time"),
            "last_update": metadata.get("last_update"),
            "chunk_count": len(metadata.get("chunk_ids", []))
        }
    
    def list_indices(self) -> List[str]:
        """List all available indices."""
        return list(self.indices.keys())
    
    def delete_index(self, index_name: str) -> bool:
        """
        Delete an index and its files.
        
        Args:
            index_name: Name of the index to delete
            
        Returns:
            True if index was deleted successfully
        """
        try:
            # Remove from memory
            if index_name in self.indices:
                del self.indices[index_name]
            if index_name in self.chunk_metadata:
                del self.chunk_metadata[index_name]
            
            # Remove files
            index_path = self.indices_dir / f"{index_name}.index"
            metadata_path = self.indices_dir / f"{index_name}_metadata.json"
            
            if index_path.exists():
                index_path.unlink()
            if metadata_path.exists():
                metadata_path.unlink()
            
            logger.info(f"Deleted index {index_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete index {index_name}: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get overall statistics about the vector store."""
        total_vectors = sum(index.ntotal for index in self.indices.values())
        
        index_stats = {}
        for name, index in self.indices.items():
            index_stats[name] = {
                "vectors": index.ntotal,
                "dimension": index.d,
                "type": self.chunk_metadata.get(name, {}).get("config", {}).get("index_type", "unknown")
            }
        
        return {
            "total_indices": len(self.indices),
            "total_vectors": total_vectors,
            "dimension": self.dimension,
            "indices": index_stats
        }
    
    def rebuild_index(self, index_name: str, vectors: np.ndarray, 
                     chunk_ids: List[str]) -> bool:
        """
        Rebuild an index from scratch.
        
        Args:
            index_name: Name of the index to rebuild
            vectors: All vectors to add to the index
            chunk_ids: All chunk IDs corresponding to vectors
            
        Returns:
            True if index was rebuilt successfully
        """
        logger.info(f"Rebuilding index {index_name}")
        
        # Delete existing index
        if index_name in self.indices:
            self.delete_index(index_name)
        
        # Create new index
        if not self.create_index(index_name, force_recreate=True):
            return False
        
        # Add all vectors
        return self.add_vectors(index_name, vectors, chunk_ids)
