# Indian Legal Document Search System

A comprehensive search system for Indian legal documents that compares 4 different similarity methods to find the most effective approach for legal document retrieval.

## Project Overview

This system implements and compares four different similarity methods:
1. **Cosine Similarity**: Standard semantic matching
2. **Euclidean Distance**: Geometric distance in embedding space  
3. **MMR (Maximal Marginal Relevance)**: Reduces redundancy in results
4. **Hybrid Similarity**: 0.6×Cosine + 0.4×Legal_Entity_Match

## Features

- **Document Processing**: Handles PDF/Word legal documents with legal-aware chunking
- **Multi-Method Search**: Parallel execution of all 4 similarity methods
- **Performance Analytics**: Precision, recall, and diversity score calculations
- **Web Interface**: 4-column comparison view with performance dashboard
- **Legal Entity Recognition**: Custom NER for Indian legal entities

## Test Dataset

- Indian Income Tax Act sections
- GST Act provisions
- Sample court judgments
- Property law documents

## Architecture

```
question2_legal_search/
├── backend/                    # Python FastAPI backend
│   ├── api/                   # API endpoints
│   ├── core/                  # Core business logic
│   ├── models/                # Data models
│   ├── services/              # Business services
│   └── utils/                 # Utility functions
├── frontend/                  # React frontend
│   ├── src/
│   │   ├── components/       # React components
│   │   ├── pages/            # Page components
│   │   ├── services/         # API services
│   │   └── utils/            # Frontend utilities
├── data/                      # Legal documents and datasets
│   ├── raw/                  # Original legal documents
│   ├── processed/            # Processed and chunked documents
│   └── embeddings/           # Generated embeddings
├── models/                    # ML models and indices
│   ├── embeddings/           # Embedding models
│   └── indices/              # FAISS indices
├── tests/                     # Test files
├── docs/                      # Documentation
└── scripts/                   # Utility scripts
```

## Technology Stack

**Backend:**
- FastAPI for REST API
- Sentence-BERT for embeddings
- FAISS for vector search
- spaCy for legal NER
- PyPDF2/python-docx for document processing

**Frontend:**
- React with Vite
- Tailwind CSS for styling
- Chart.js for performance visualization
- Axios for API communication

## Getting Started

### Prerequisites
- Python 3.8+
- Node.js 16+
- Git

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd question2_legal_search
```

2. Set up backend
```bash
cd backend
pip install -r requirements.txt
```

3. Set up frontend
```bash
cd frontend
npm install
```

4. Configure environment
```bash
cp .env.example .env
# Edit .env with your configuration
```

### Running the Application

1. Start the backend server
```bash
cd backend
python main.py
```

2. Start the frontend development server
```bash
cd frontend
npm run dev
```

3. Access the application at `http://localhost:5173`

## Test Queries

- "Income tax deduction for education"
- "GST rate for textile products"
- "Property registration process"
- "Court fee structure"

## Performance Metrics

The system evaluates each similarity method using:
- **Precision**: Relevant documents in top 5 results
- **Recall**: Coverage of relevant documents
- **Diversity Score**: Result variety (for MMR evaluation)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
